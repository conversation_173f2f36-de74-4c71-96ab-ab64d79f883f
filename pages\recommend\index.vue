<template>
	<tm-app>
		<view class="main">
			<image :src="store.setting.invite_bgimg" class="header" mode="widthFix"></image>
			<view class="content">
				<view class="card" v-if="store.setting.invit_intro">
					<view class="ctit">活动说明</view>
					<view class="line"></view>
					<tm-html :content="store.setting.invit_intro" :tag-style="tagStyle" :container-style="htmlStyle"></tm-html>
				</view>
				<view class="card card2" v-if="store.setting.invite_lp[0]">
					<view class="ctit">精美礼品</view>
					<view class="line"></view>
					<view class="photo-wall">
						<image 
						:src="item.src" 
						class="photo" 
						mode="aspectFill" 
						v-for="item in store.setting.invite_lp" 
						:key="item.id"></image>
					</view>
					<text class="tip">{{store.setting.invit_gift_intro}}</text>
				</view>
				<button open-type="share" @click="appshare(tmstore)">
					<image src="/static/img/recommend-button.png" class="recommend-button" mode="widthFix"></image>
				</button>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import { useStore } from '@/until/mainpinia';
	import { useTmpiniaStore } from '@/tmui/tool/lib/tmpinia'
	import tmHtml from '@/tmui/components/tm-html/tm-html.vue'
	import { share } from "@/tmui/tool/lib/share";
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const tmstore = useTmpiniaStore();
	const store = useStore()
	const appshare = (tmstore)=>{
		// #ifdef APP-PLUS
		uni.share({
			provider: 'weixin',
			scene: "WXSceneSession",
			type: 5,
			imageUrl: store.setting.invite_lp_share,
			title: tmstore.tmStore.wxshareConfig_miniMp.title,
			miniProgram: {
				id: 'gh_526d20f68d21',
				path: `/pages/index/index?pid=${store.userinfo.id}`,
				type: 0,
				webUrl: 'https://wansao.com/'
			},
			success: function (res) {
			 console.log("success:" + JSON.stringify(res));
			},
			fail: function (err) {
				console.log("fail:" + JSON.stringify(err));
				uni.showModal({
					title: '分享失败',
					content: '您的手机上未安装微信',
					showCancel:false,
				});
			}
		});
		// #endif
	}
	/* 
	const shareFunction = ()=>{
		return {
			title:tmstore.tmStore.wxshareConfig_miniMp.title,
			imageUrl:tmstore.tmStore.wxshareConfig_miniMp.imageUrl,
			desc:tmstore.tmStore.wxshareConfig_miniMp.desc,
			path:'/pages/index/index?pid='+store.userinfo.id
		}
	}
	onShareAppMessage(shareFunction)
	onShareTimeline(shareFunction) */
	
	const tagStyle = {
		image:'max-width:100%',
		text:'font-size: 20rpx;line-height: 40rpx;text-align: center;',
		view:'font-size: 20rpx;line-height: 40rpx;text-align: center;',
		table:'max-width:100%'
	}
	const htmlStyle = 'padding: 20rpx;;width:594rpx'
	
</script>

<style lang="less" scoped>
	.main {
		padding-bottom: 200rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #ff2b49;
		
		.header {
			width: 750rpx;
			height: 1057rpx;
			flex-shrink: 0;
		}

		.content {
			margin-top: -350rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			.card {
				width: 694rpx;
				min-height: 313rpx;
				background: #FFFFFF;
				box-shadow: 0rpx 0rpx 18rpx 0rpx rgba(208,19,22,0.86), inset 0rpx 0rpx 12rpx 0rpx rgba(255,172,12,0.82);
				border-radius: 15rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				position: relative;
				padding:30rpx 0 30rpx;
				margin-bottom: 30rpx;
				color: #202020;
				.ctit{
					font-size: 42rpx;
					font-weight: 500;
					color: #FF2C49;
				}
				.line{
					margin-top: 30rpx;
					margin-bottom: 20rpx;
					width: 100%;
					height: 10rpx;
					background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmEAAAAHCAMAAAB3GWoXAAAAPFBMVEUAAAD8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVr8aVoTbB+FAAAAE3RSTlMAM0SZHHVgUUgtjH7hbD4kCBIG6bzolwAAAIdJREFUWMPt1MESgyAMRVFoFAWrYv3/f+1j+rpwJbrCTO7qLJkkg9NQ9Cr7OKuR3rvKOmc1kl1Yyw0iMtMj3NMenugIJ3oVRHfF+edc/J9J8UonONITvNA9PNIzPNABDtcet+tMKiYXbkwunax1K94q1uqrHqch+8OsQ3ZhdmEPa3mpLDsNfQEVQVXxmamOQgAAAABJRU5ErkJggg==') no-repeat center center/100% auto;
				}
				.p2{
					font-size: 26rpx;
					.sp{
						color: #FF2C49;
						font-size: 34rpx;
						font-weight: bold;
					}
				}
			}
			.card2{
				width: 694rpx;
				min-height: 982rpx;
				.photo-wall{
					width: 530rpx;
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					align-items: center;
					flex-wrap: wrap;
					.photo{
						width: 243rpx;
						height: 329rpx;
						margin: 20rpx 10rpx;
					}
				}
				.tip{
					color: #716D6E;
					font-size: 26rpx;
				}
			}
			button{
				background-color: transparent !important; 
				&::after{
					display: none;
				}
			}
			.recommend-button{
				margin-top: 40rpx;
				width: 458rpx;
			}
		}

	}
</style>