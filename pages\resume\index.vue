<template>
	<tm-app ref="app">
		<view class="main" v-if="auntData?.name">
			<view class="normal-pad">
				<view class="pad-content">
					<view class="user-header smallbg">
						<view class="user-photo">
							<image :src="auntData.photo" class="user-photo-a" mode="widthFix"
								@click='previewImgs([auntData.photo],auntData.photo)'></image>
						</view>
						<view class="user-info">
							<view class="titlestrong">
								<text>{{auntData.name}}</text>&nbsp;
								<text v-if="auntData.age">{{auntData.age}}</text>
							</view>
							<view class="title-small">
								<text v-if="auntData.years">{{auntData.years}}</text>
								<text v-if="auntData.area">{{auntData.area}}</text>
							</view>
							<view class="title-small" v-for="(item,index) in auntData.job" :key="'job'+index">
								<text>{{item}}</text>
							</view>
						</view>
						<view class="user-icon" v-if="auntData.signPic">
							<image :src="auntData.signPic" mode="widthFix"></image>
						</view>
					</view>

					<view class="user-bs" v-if="certPic.length>0">
						<view class="user-bs-item" v-for="(item,index) in certPic" :key="index">
							<image :src="item.pic" class="user-bs-ico" mode="aspectFill"></image>
							<text>{{item.name}}</text>
						</view>
					</view>

				</view>
			</view>

			<view class="normal-pad">
				<view class="pad-content">
					<view class="titlestrong">
						<view class="line-header-l"></view>自我介绍
					</view>
					<view class="p-content bigbg">
						<kevy-ellipsis :content="auntData.content" :rows="6" :font-size="26" :line-height="42"
							fontColor="#000" ref="ke1" />
						<view class="more" @click="showContent" v-if="!show1">
							<text>查看完整</text>
							<tm-icon rotate :rotate-deg="0" :font-size="20" color="#909399" name="tmicon-angle-down"></tm-icon>
						</view>
						<view class="more" @click="showContent" v-if="show1">
							<text>收起全部</text>
							<tm-icon rotate :rotate-deg="0" :font-size="20" color="#909399" name="tmicon-angle-up"></tm-icon>
						</view>
					</view>
				</view>
			</view>

			<view class="normal-pad border-shadow" v-if="certPic.length>0">
				<view class="pad-content">
					<view class="titlestrong">
						<view class="line-header-l zise">
						</view>阿姨认证
					</view>
					<view class="p-content bigbg">
						<template v-for="(item,index) in certPic" :key="index">
							<view class="rz-item" @click='item.show?previewImgs(item.imgs,""):null'>
								<image :src="item.pic" class="user-bs-ico" mode="aspectFill"></image>
								<view class="rz-rightbox">
									<view class="rz-title">{{item.desc}}{{item.show?'（查看证书）':''}}
										<view v-if="item.show" class="icon">
											<tm-icon :font-size="24" name="tmicon-angle-right" color="#999"></tm-icon>
										</view>
									</view>
									<view class="red-font" v-if="item.tpl">{{item.tpl}}</view>
								</view>
							</view>
						</template>
					</view>
				</view>
			</view>

			<view class="normal-pad" v-if="skillsInfo.length>0">
				<view class="pad-content">
					<view class="titlestrong">
						<view class="line-header-l lsbg"></view>阿姨技能
					</view>
					<view class="p-content bigbg">
						<template v-for="(item,index) in skillsInfo" :key="index">
							<view class="jn-item">
								{{item.name}}
							</view>
						</template>
					</view>
				</view>
			</view>

			<view class="normal-pad border-shadow" v-if="clientResumePhoto.length > 0">
				<view class="pad-content">
					<view class="titlestrong">
						<view class="line-header-l slbg"></view>阿姨相册
						<view class="mores" @click="goLink('/pages/more_one/index',{hid})">更多</view>
					</view>
					<view class="p-content bigbg clearfix">
						<tm-image-group>
							<tm-image :width="144" :height="144" :margin="[6,6]" :round="6" preview
								:previewList="clientResumePhoto" previewName="file_path" :src="item.imgpath"
								:key="item.imgpath" model="aspectFill" v-for="item in clientResumePhoto"></tm-image>
						</tm-image-group>
					</view>
				</view>
			</view>

			<view class="normal-pad border-shadow" v-if="planType == 1">
				<view class="pad-content dq-content">
					<view class="titlestrong">
						<view class="line-header-l zise"></view>阿姨档期
						<view class="small-info">说明：标红天数为该月已安排的天数</view>
					</view>
					<view class="p-content bigbg">
						<view class="list">
							<view class="line" v-for="(item,index) in workingDate" :key="index">
								<view class="bar">
									<view class="container">
										<tm-progress :width="304" :height="22" color="#fb243c"
											v-model:percent="item.percent"></tm-progress>
									</view>
								</view>
								<text class="day">{{item.day}}月</text>
								<text class="day">{{item.all}}天</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="normal-pad border-shadow" v-if="evaluations.length > 0">
				<view class="pad-content bigbg">
					<view class="titlestrong">
						<view class="line-header-l"></view>雇主评价
						<view class="mores" @click="goLink('/pages/more_two/index',{hid})">更多</view>
					</view>

					<view class="p-content" v-for="(item,index) in evaluations" :key="index">
						<view class="pj-title">
							<view> {{item.nickName}}评价:</view>
							<view>共<text class="day">{{item.days}}</text>天</view>
						</view>
						<view class="show-p">
							{{item.content}}
						</view>
						<view class="item-xc">
							<image :src="item2" @click='previewImgs(item.pics,item2)' class="item-xc-img" mode="aspectFill"
								v-for="(item2,index2) in item.pics" :key="index2"></image>
						</view>
					</view>
				</view>
			</view>

			<view class="normal-pad">
				<view class="pad-content">
					<view class="titlestrong">
						<view class="line-header-l bgorange"></view>联系方式
					</view>
					<view class="p-content smallbg">
						<view class="f28 tc">安徽省皖嫂家政服务平台由省妇联创办于2001年</view>
						<view class="f28 tc">点击拨打电话免费咨询</view>
						<view class="tc callnumber" @click="call()">
							<view class="icon-phone" mode="widthFix"></view> {{store.servicephone}}
						</view>
					</view>
				</view>
			</view>

			<view class="normal-pad">
				<view class="pad-content">
					<view class="titlestrong">
						<view class="line-header-l bgorange"></view>温馨提醒
					</view>
					<view class="p-content smallbg">
						<view class="f28">{{tips}}</view>
					</view>
				</view>
			</view>

			<view class="float">
				<view class="left">
					<view class="container" @click="goLink('/pages/index/index')">
						<image src="../../static/img/home.png" class="icon"></image>
						<text class="text">首页</text>
					</view>
					<button class="container" open-type="share" :data-detail="detail" @click="appshare">
						<image src="../../static/img/share2.png" class="icon"></image>
						<text class="text">分享</text>
					</button>
				</view>
				<view class="right">
					<view class="button button2" @click="call()">电话咨询</view>
					<view class="button button3" @click="goApp(1)">立即预约</view>
				</view>
			</view>
			<view class="mask" v-if="afresh" @click="call(afreshData.tel)">
				<image :src="afreshData.src" mode="widthFix" class="facead"></image>
			</view>
		</view>
	</tm-app>
</template>
<script lang="ts" setup>
	import { ref, computed, getCurrentInstance } from "vue"
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from "@/tmui/components/tm-app/tm-app.vue"
	import tmImage from '@/tmui/components/tm-image/tm-image2.vue'
	import tmProgress from '@/tmui/components/tm-progress/tm-progress.vue'
	import * as api from '@/api/index.js'
	import { goLink } from '@/until/index'
	import { useStore } from '@/until/mainpinia';
	import kevyEllipsis from '@/components/kevy-ellipsis/kevy-ellipsis'
	// import { share } from "@/tmui/tool/lib/share";
	// const { onShareAppMessage, onShareTimeline } = share();
	// onShareAppMessage();
	// onShareTimeline()

	// 分享功能
	import { onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
	const shareFunction = ({ from, target }) => {
		if (from === 'button' && target?.dataset?.detail?.auntData?.name) {
			return {
				title: target.dataset.detail.auntData.job[0]?target.dataset.detail.auntData.job[0]:'' + target.dataset.detail.auntData.name + '的个人简历',
				imageUrl: target.dataset.detail.auntData.photo,
				desc: '我在查看' + target.dataset.detail.auntData.name + '的个人简历',
				path: `/pages/resume/index?hid=${target.dataset.detail.auntData.hid}&source=resume&pid=${store.userinfo.id}`,
			}
		} else {
			return {
				title: '',
				imageUrl: '',
				desc: ''
			}
		}
	}
	onShareAppMessage(shareFunction)
	onShareTimeline(shareFunction)


	// 页面数据
	const hid = ref('')
	const store = useStore()
	const resume_bg = computed(()=>store.setting.resume_bg)
	const detail = ref({})
	const certPic = ref([])
	const skillsInfo = ref([])
	const clientResumePhoto = ref([])
	const workingDate = ref([])
	const evaluations = ref([])
	const planType = ref(null)
	const tips = ref(null)
	const auntData = ref<Record<string, any>>({})
	const afresh = ref('')
	const afreshData = ref<Record<string, any>>({})
	onLoad((p) => {
		hid.value = p.hid
		getData(hid.value)
	})
	const getData = (hid : string) => {
		api.request.ajax({
			url: '/aunt/auntResumeInfoClient',
			type: 'POST',
			whiteList: true,
			data: { hid }
		}).then(res => {
			if (res.code === 1) {
				res.data.auntData.content = res.data.auntData.content.replace(/\s+/g, ' ')
				detail.value = res.data
				auntData.value = res.data.auntData
				certPic.value = res.data.certPic
				skillsInfo.value = res.data.skillsInfo
				clientResumePhoto.value = res.data.newClientResumePhoto
				planType.value = res.data.planType
				tips.value = res.data.tips
				workingDate.value = res.data.workingDate
				evaluations.value = res.data.evaluations
				afresh.value = res.data.afresh
				afreshData.value = res.data.afreshData
			} else {
				uni.showToast({ title: res.msg, icon: 'none' })
			}
		})
	}

	// 电话和预览
	const call = (tel?:string) => uni.$tm.u.callPhone(tel||store.servicephone)
	const previewImgs = (imgs : string[],current:string) => {
		uni.previewImage({
			urls: imgs,
			current:current
		})
	}
	const goApp = (type) => {
		store.$patch(state => {
			state.thisAppointmentType = type
		})
		goLink('/pages/order/index')
	}
	const ke1 = ref(null)
	const show1 = ref(false)
	const showContent = ()=>{
		ke1.value.changeCollapse()
		show1.value = !show1.value
	}
	const appshare = ()=>{
		// #ifdef APP-PLUS
		uni.share({
			provider: 'weixin',
			scene: "WXSceneSession",
			type: 5,
			imageUrl: detail.value.auntData.photo,
			title: detail.value.auntData.job[0]?detail.value.auntData.job[0]:''+ detail.value.auntData.name + '的个人简历',
			miniProgram: {
				id: 'gh_526d20f68d21',
				path: `/pages/resume/index?hid=${detail.value.auntData.hid}&source=resume&pid=${store.userinfo.id}`,
				type: 0,
				webUrl: 'https://wansao.com/'
			},
			success: function (res) {
				console.log("success:" + JSON.stringify(res));
			},
			fail: function (err) {
				console.log("fail:" + JSON.stringify(err));
				uni.showModal({
					title: '分享失败',
					content: '您的手机上未安装微信',
					showCancel:false,
				});
			}
		});
		// #endif
	}
</script>
<style lang="less" scoped>
	.main {
		width: 750rpx;
		padding-bottom: 200rpx;
		background: #fff;
		display: flex;
		flex-direction: column;
		align-items: center;
		overflow: hidden;
		.bigbg{
			background-image: v-bind("'url(' + resume_bg +')'");
			background-size: 270rpx 270rpx;					
			background-repeat: no-repeat;
			background-position:center;
		}
		.smallbg{			
			background-image: v-bind("'url(' + resume_bg +')'");
			background-size: 155rpx 155rpx;					
			background-repeat: no-repeat;
			background-position:center;
			
		}
		.mask{
			width: 750rpx;
			height: 100vh;
			overflow: hidden;
			background-color: rgba(0,0,0,0.6);
			position: fixed;
			top: 0;
			left:0;
			display: flex;
			justify-content: center;
			align-items: center;
			.facead{
				width: 700rpx;
			}
		}
		.normal-pad {
			width: 690rpx;
			background: #fff;
			border-radius: 30rpx;
			margin: 30rpx 30rpx 0 30rpx;
			box-shadow: 0 0 10rpx #d3d3d3;
			border-radius: 30rpx;

			.pad-content {
				padding: 30rpx;

				.user-header {
					display: flex;

					.user-photo {
						width: 135rpx;
						height: 135rpx;
						border-radius: 50%;
						overflow: hidden;
						box-shadow: 0 4rpx 8rpx #e8e8e8;
						border: 5rpx solid #fff;

						.user-photo-a {
							width: 135rpx;
							height: 135rpx;
						}
					}

					.user-info {
						padding-left: 30rpx;

						.titlestrong {
							font-weight: bold;
							font-size: 30rpx;
							padding-bottom: 5rpx;
						}

						.title-small {
							color: #606060;
							line-height: 1.6em;
						}
					}

					.user-icon {
						padding-left: 20rpx;

						image {
							width: 160rpx;
							height: 160rpx;
						}
					}
				}

				.user-bs {
					margin: 20rpx 0 10rpx;
					width: 100%;
					display: flex;
					justify-content: flex-start;

					.user-bs-item {
						width: 20%;
						margin-right: 7%;
						display: flex;
						align-items: center;

						// justify-content: center;

						&:nth-last-child(1) {
							margin-right: 0;
						}
					}
				}

				.titlestrong {
					font-weight: bold;
					font-size: 30rpx;
					padding-bottom: 5rpx;
					display: flex;
					align-items: center;
					position: relative;

					.line-header-l {
						width: 12rpx;
						border-radius: 10rpx;
						height: 30rpx;
						background: #fb243c;
						margin-right: 20rpx;
					}

					.zise {
						background: #a880f9
					}

					.lsbg {
						background: #6dd9d4;
					}

					.bgorange {
						background: #f38a55;
					}

					.mores {
						font-size: 28rpx;
						color: #666;
						position: absolute;
						right: 20rpx;
					}

					.small-info {
						font-size: 22rpx;
						color: #888;
						font-weight: normal;
						position: absolute;
						right: 20rpx;
					}
				}
				.more {
					margin-top: 16rpx;
					margin-bottom: -20rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						font-size: 25rpx;
						font-weight: 300;
						color: #909399;
						margin-right: 8rpx;
					}
				}
				
				.p-content {
					padding: 20rpx 0;
					line-height: 40rpx;
					
					.rz-item {
						border-bottom: 1rpx solid #f3f3f3;
						padding-bottom: 20rpx;
						padding-top: 20rpx;
						display: flex;
						position: relative;

						&:nth-last-child(1) {
							border-bottom: 0;
							padding-bottom: 0;
						}

						.rz-rightbox {
							padding-left: 10rpx;

							.rz-title {
								font-size: 28rpx;
								padding-bottom: 4rpx;
							}

							.red-font {
								color: #fb243c;
							}

							.icon {
								position: absolute;
								right: 20rpx;
								top: 40rpx;
							}
						}
					}

					.jn-item {
						padding-left: 35rpx;
						background-size: 24rpx 26rpx;
						background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAaCAYAAACtv5zzAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyFpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDozNUNEMkFFRkI4MjMxMUU5QUU0N0IzRUNENTVBRThBRiIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDozNUNEMkFGMEI4MjMxMUU5QUU0N0IzRUNENTVBRThBRiI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjM1Q0QyQUVEQjgyMzExRTlBRTQ3QjNFQ0Q1NUFFOEFGIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjM1Q0QyQUVFQjgyMzExRTlBRTQ3QjNFQ0Q1NUFFOEFGIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+O6+D5AAAAbRJREFUeNpi/K1iw0AJYLl9GIX/R9VWDUhtAmIpIC5lYaAiABrOAaRWA7E6VGgCEwN1QTcQ6yHxrzNR0fU+QCoHTbiWiUqGg8J7PprwEWD8bGWiguEgMxYCsQiaVCWIoIYPSoHYBU1sK9D1R6hhgSkQt6CJ/QfiahiHEgt4gXg5KCugiS8Huv4iNSyYCsTKaGK/gbgeWYBcC6KBOBaL+Byg6+9QagHI1dOxiH8H4mZ0QVItYAXipdDwRwcTga5/TqkFjUBsjkX8IxB3YtMAsoALiCOAWJOA4U5AXIFDrgPo+g+4LNgCTW5XoTlSDos6UC5dDMSMWORegIIHl6tAFthB2SDNcUB8E1oqCiCJz4WW79hAE9D13/FZMA9NDFSmlwDxfShdCMR+OPTfBSVNfOHKCKzR2ID0biSfkJQfgK5fhk8ByAe/gDgQiG+RaDioOFhBSBEsmb4DYm8gfkuCBaAC7R+xFoAAKIsHQH1ECICK4q3EuIIJi8ZEIvRVEetNbDkZFGl1ePRsA+LDlFjAAK1EFmMR/wmtwRgotQBUK6UA8TQg/gzEf4H4FBC7AvE1UiwACDAAYLtdNsp1EK4AAAAASUVORK5CYII=);
						background-repeat: no-repeat;
						background-position: left center;
						line-height: 1.8em;
					}

					.list {
						width: 100%;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: space-between;

						.line {
							display: flex;
							flex-direction: column;
							align-items: center;
							width: 40rpx;

							.bar {
								height: 304rpx;
								display: flex;
								flex-direction: column;
								align-items: center;
								justify-content: center;

								.container {
									width: 304rpx;
									transform: rotate(270deg);
								}
							}

							.day {
								margin-top: 6rpx;
								font-size: 26rpx;
								line-height: 30rpx;
								white-space: nowrap;
							}

						}
					}

					.pj-title {
						display: flex;
						justify-content: space-between;
						color: #999;

						.day {
							color: red
						}
					}

					.show-p {
						padding: 20rpx 0 30rpx;
						font-size: 26rpx;
					}

					.item-xc {
						width: 100%;
						display: flex;
						align-items: center;
						flex-wrap: wrap;
						margin: 0 -10rpx;

						.item-xc-img {
							width: 136rpx;
							height: 136rpx;
							border-radius: 20rpx;
							margin: 10rpx;
						}
					}

					.tc {
						text-align: center;
						display: flex;
						justify-content: center;

					}

					.f28 {
						font-size: 28rpx;
					}

					.callnumber {
						margin-top: 20rpx;
						display: flex;
						align-items: center;
						font-size: 40rpx;
						font-weight: bold;
						color: #fb243c;

						.icon-phone {
							background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyFpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDowMDQ5Q0JGNEI4QkYxMUU5OEZCMkExOTUzQUVFQUM3NiIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDowMDQ5Q0JGNUI4QkYxMUU5OEZCMkExOTUzQUVFQUM3NiI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjAwNDlDQkYyQjhCRjExRTk4RkIyQTE5NTNBRUVBQzc2IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjAwNDlDQkYzQjhCRjExRTk4RkIyQTE5NTNBRUVBQzc2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+nk0NfgAABgFJREFUeNrEmnlsFVUUxqfTYiuUxYKRSmxqCy0qLqERrdUIroBGBDeUgI0LKkriHyaKGiVq0ISoIETBtEoJKKCikiguaAvIYoMLIotKrCgIRCiWKiEWit+x3w2nl7lv3ry+wZP8kpl50zv33OVstxktfS/20iC9wWBQDvqDItATdAVHwF9gL9gKfgCrQS34o6MfzuiAAtLB28BYcH6KbawFc8ECsO94KZAPHgZ3g86Od1rAHo68x5noBbIc7/8NZoIXwe64FOgEHgSTAzr+E1gK6sB6sA0ctt7J4tI6F1wOhoEC651m8Dh4GRxKpwL9wFv8uB7lOaAK1Kcy++BScC+4mfdGpL3RoCGsET+JD40EX1udfx30BeNT7LzHzV3Hjp4BlqjfBvGbwzqqwD3gbZDL+59BBbgD/OqlT8QyjSDGMvUA74NxqSpwH5il3nkPDKQJjEuW8Btr1L6roaWLpMANtApG5PpG0OTFL9vBFeAD9awaXJ2sAv2ptfltBpgYYFXilANgFPhYzcSbAVbrGAWywSLQhfeLaTr/D/mHK+Fb3p8EFoLMRAo8BM5WG7YStDo+cCYVvCZGJcTB3aQc4oXcm4F+oIDWIIcmrkJtJluuA/NpncQfDAfLYlREvP6rvN4Piunp283Ao+y82TSuzovNPk2Z1k40dxfEqEC18jfduFLazcAp4BcqIGuvEOx0NLYRrAK/gafU80Z61u9jUuJK8IkKOWQQm8wMjFOjPydB5wu59k0gN039lgc+AqfHpMCn4CsVHN6il5D2drNCYiIjj4AdDIeN9OGHesSkRJW6vt0oIKM6gA83gW8SNNBi3U8FK604pphxUhyySEWpkjz1FAUuUy8sDWmgMeDZbNrn5TS5El2ui0mBRtW2RK9DfNpWI3UhDWwBBwOcoUSn0zkYs2N2cMvVdbl8vFQ9WJ+Ed6wNeH4CKLMaj0s2qOtSrcABmsYwqXE8P3icQowf1XWJT5PkRagQvOtQ9H46mbhF9zPPV/ntnxGCrGccpZXXrNQwrkjVSK6fYiPVKkq084jpMSuQYVsQE+l1j9DIYXrjoBxBcocnY1QgV13/F0o0qUJVFFnHEkuQTOaeiEN6q+t9vtrVsplPjdjYFMY/QfKSiVcCRDb7JGZ/UaVEWyRRYLN6cE7ExlpZFtnsSFfn2wkI5GTmDqL85ww9oogu72zyrbh/SAoj0sSsbEfAb5msstVwiWYzfjK11HzmvfkRvjdYXa8VBT5TD4anuC4bWDXY4/hdot1djGVWWDNWzDg/GSPSR60Smf1an7G/CZAGWFMURTYyFtrl+D2LPmcm/Yh2hvLdDz13sdjIGGVGV+qERsf04zsYp1zitZ0DuORE8ArTQj1jF7EK6CeoYd1phzQmpZT1uY3lFIlpihJkZcmIlECk5n9VSEgwkcl6N2uZ/B7w/mjWhsy+k5Sy2Wi7V2X9OQnse7IihxVDOcquIE+s0XPgAfXOFEfns638exrz4nZllXyWVcyxkGQ8X6bB8UiO/Kx3bAld750XuAqmOtqQM4OnVczWL6isslONvHxonopUOyINnP5SjvgGq1iWSRPs6ryU2p9Q95P03rEPOMRSrFZ2ejErY61pDgdyaDabregyaJnVM28X+YKlm1ZXafEQ3b8JrUdZpZN0iaz53SGd78ocvVDt01vtwfQdUz5GZf9iKWZ4yZ3mpEtMuFGmqiGyDLcH2dYgEadyl7oXS/FOxJA7VTmL3nqQ5cmXuZxDoty3Us3E9V7buVVFTB33WZKpV1FqC1fDgkR/FJbAj1B7oogbScooBWnsvJR2VtFDd1a+5FrwRpjWYSLLaaDX/jSykuFClVVXiiLinEayTLPGakdO8M/zjhZz3fllhINusdcTGIjZ1YetDIulMPad13Y4Yh9Ud2EyUsagbyhDDjs0f8xrq88mdaSVyr8a9GKIMCGBo5PO76edz+Dm7x6SU8jyeT5BSJ42BXRaKKZtLDd21HLKEYbE4vEXUuHoJYo0/btNHj1kOUOGEo54rnf0321klOV/KrZwza/wgovFkeRfAQYAliBbdMqmnhgAAAAASUVORK5CYII=);
							background-repeat: no-repeat;
							background-position: left center;
							background-size: 100% 100%;
							height: 48rpx;
							width: 48rpx;
							margin-right: 10rpx;
						}
					}

				}

				.user-bs-ico {
					width: 50rpx;
					height: 50rpx;
					margin-right: 8rpx;
				}

			}
		}










		.button1 {
			width: 458rpx;
			height: 84rpx;
			background: linear-gradient(180deg, #F95959, #F73030);
			border-radius: 42rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin: 20rpx 0 40rpx;
			font-size: 36rpx;
			color: #FFFFFF;
			line-height: 84px;
			letter-spacing: 4rpx;
		}

		.float {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 750rpx;
			background: rgba(255, 255, 255, .9);
			backdrop-filter: blur(8rpx);
			box-shadow: 1rpx 1rpx 16rpx 0rpx rgba(194, 186, 186, 0.5);
			padding: 20rpx 60rpx 20rpx 78rpx;
			padding-bottom: 0;
			padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left {
				width: 150rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.container {
					display: flex;
					flex-direction: column;
					align-items: center;

					&:after {
						display: none;
					}

					.icon {
						width: 30rpx;
						height: 30rpx;
					}

					.text {
						width: 44rpx;
						height: 30rpx;
						line-height: 30rpx;
						margin-top: 10rpx;
						font-size: 22rpx;
						color: #413D3E;
					}
				}
			}

			.right {
				width: 400rpx;
				height: 72rpx;
				border: 1rpx solid #F83B3B;
				border-radius: 36rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				overflow: hidden;

				.button {
					font-size: 36rpx;
					height: 70rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					flex: 1;
				}

				.button2 {
					background-color: #fff;
					color: #F83B3B;
				}

				.button3 {
					background-color: #F83B3B;
					color: #fff;
				}
			}
		}
	}
</style>