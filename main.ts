
import { createSSRApp } from "vue";
import * as <PERSON><PERSON> from 'pinia';
import tmui from "./tmui"
import App from "./App.vue";
import * as api from './api/index.js'

// #ifdef APP-PLUS
import { addPermisionInterceptor } from '@/uni_modules/x-perm-apply-instr/js_sdk/index.js'
// addPermisionInterceptor('chooseImage', '为了修改个人头像和发布信息图片视频等, 我们需要申请您设备的存储权限')
// addPermisionInterceptor('chooseVideo', '为了发布信息图片视频等, 我们需要申请您设备的相机和存储权限')
// addPermisionInterceptor('saveImageToPhotosAlbum', '为了保存推广海报到手机相册, 我们需要申请您设备的存储权限')
// addPermisionInterceptor('getLocation', '为了根据您的位置展示信息, 我们需要申请您设备的位置权限')
addPermisionInterceptor('makePhoneCall', '为了联系客服/用户/咨询等, 我们需要申请您设备的拨打电话权限')
// addPermisionInterceptor('getRecorderManager', '为了使用语言消息功能等, 我们需要申请您设备的麦克风权限')

if (uni.getSystemInfoSync().platform === "android") {
  uni.showModalReset = uni.showModal;
  uni.showModal = function({
    title,
    content,
    showCancel,
    cancelText,
    cancelColor,
    confirmText,
    confirmColor,
    editable,
    placeholderText,
    success,
    fail,
    complete,
  }) {
    uni.showModalReset?.({
      title,
      content,
      showCancel,
      cancelText: confirmText || '确定',
      cancelColor: confirmColor || '',
      confirmText: cancelText || '取消',
      confirmColor: cancelColor || '',
      editable,
      placeholderText,
      success: (res: { confirm: boolean; cancel: boolean }) => {
        const result = res.confirm ? { cancel: 1 } : { confirm: 1 };
        success && success(result);
      },
      fail: (err: any) => {
        fail && fail(err);
      },
      complete: (res: any) => {
        complete && complete(res);
      },
    });
  };
}

// #endif

export function createApp() {
	const app = createSSRApp(App);
	app.use(Pinia.createPinia());
	app.use(tmui)
	app.mixin({
		onLoad: function (e) {
			if (e.title) {
				wx.setNavigationBarTitle({
					title: e.title
				})
			}
		},
		onUnload: function () {
			api.request.cancelPageRequest(api.request.getCurrentPage())
		}
	})
	return {
		app,
		Pinia
	};
}