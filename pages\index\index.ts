export function splitArray(arr) {
	let temp = arr.map(item=>!!item.pics[0])
	// 统计数组中true的数量
	const trueCount = temp.filter(item => item === true).length;
	
	let firstArray = [];
	let secondArray = [];
	let trueCountInFirstArray = 0;
	let falseCountInFirstArray = 0;
	let targetTrueCount = 0
	let targetFalseCount = 0
	
	
	if(trueCount%2===1){
		targetTrueCount = Math.floor(trueCount / 2)+1
		targetFalseCount  = arr.length/2 - targetTrueCount
	}else{
		targetTrueCount = trueCount / 2
		targetFalseCount = arr.length/2 - targetTrueCount
	}
	
	arr.forEach(item=>{
		if(!!item.pics[0]){
			if(trueCountInFirstArray<targetTrueCount){
				trueCountInFirstArray+=1
				firstArray.push(item)
			}else{
				secondArray.push(item)
			}
		}else{
			if(falseCountInFirstArray<targetFalseCount){
				falseCountInFirstArray+=1
				firstArray.push(item)
			}else{
				secondArray.push(item)
			}
		}
	})
	return [firstArray, secondArray];
}
