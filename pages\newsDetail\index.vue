<template>
	<tm-app>
		<view class="main">
			<view class="title">{{detail.title}}</view>
			<view class="date">{{detail.date}}</view>
			<view class="content">
				<tm-html :content="detail.content" :tag-style="tagStyle" :container-style="htmlStyle" scroll-table></tm-html>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref,computed } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import tmText from '@/tmui/components/tm-text/tm-text.vue'
	import tmHtml from '@/tmui/components/tm-html/tm-html.vue'
	import { useStore } from '@/until/mainpinia';
	import * as api from '@/api/index.js'
	import { goLink,goPage } from '@/until/index'
	import { share } from "@/tmui/tool/lib/share";
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage()
	onShareTimeline()

	const tagStyle = {
		image:'max-width:100%',
		text:'font-size: 20rpx;line-height: 40rpx',
		view:'font-size: 20rpx;line-height: 40rpx',
		table:'max-width:100%'
	}
	const htmlStyle = 'padding: 20rpx;;width:100%'
	const detail = ref({
		title:'',
		date:'',
		content:''
	})
	const getData = (id)=>{
		api.request.ajax({
			url: '/news/detail',
			type: 'POST',
			whiteList: true,
			data:{id}
		}).then(res => {
			if(res.code===1){
				detail.value = res.data
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	onLoad(({id}) => {
		getData(id)
	})
</script>

<style lang="less" scoped>
	.main {
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #fff;
		padding: 50rpx 0;
		.title{
			font-size: 28rpx;
			font-weight: bold;
			width: 100%;
			padding: 0 20rpx;
			text-align: center;
		}
		.date{
			margin-top: 20rpx;
			color: #999;
		}
		.content{
			width: 750rpx;
			padding: 36rpx;
		}
	}
</style>