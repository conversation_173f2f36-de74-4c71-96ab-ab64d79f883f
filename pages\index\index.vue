<template>
	<tm-app ref="app">
		<view class="main">
			<view>
				<!-- #ifndef MP-ALIPAY -->
				<view class="status_bar" :style="{ height: statusBarHeight }"></view>
				<!-- #endif -->
				
				<!-- #ifdef MP-WEIXIN -->
				<tm-text _class="text-align-center mt-60" :font-size="32" color="#202020" label="皖嫂家政"></tm-text>
				<!-- #endif -->
			</view>
			<view class="swipe">
				<tm-carousel autoplay :round="3" :width="717" :height="320" model="dot" color="#FCD9DB" :list="banner"
					rangKey="src" @click="i=>goPage(banner,i)"></tm-carousel>
			</view>
			<view class="button-area">
				<view class="top-tip">
					<view class="tip">
						<tm-image :margin="[8,0]" :width="18" :height="17" src="/static/img/tipicon1.png"></tm-image>
						<tm-text :font-size="20" color="#F34450" label="找月嫂·来皖嫂"></tm-text>
					</view>
					<view class="tip">
						<tm-image :margin="[8,0]" :width="18" :height="18" src="/static/img/tipicon2.png"></tm-image>
						<tm-text :font-size="20" color="#F34450" label="皖嫂家政·互联网家政领军者"></tm-text>
					</view>
				</view>
				<view class="button-list">
					<template v-for="(item,index) in menu" :key="index">
						<view class="item" @click="goLink(item.url,{navtype:item.navtype,appid:item.appid,title:item.title})" v-if="item.navtype!==3||nowvideoshow!==0">
							<tm-image :width="76" :height="76" :src="item.icon"></tm-image>
							<tm-text parentClass="text" _class="nowrap" :font-size="26" color="#656462"
								:label="item.title"></tm-text>
						</view>
					</template>

				</view>
			</view>
			<view class="area">
				<tm-carousel autoplay :round="3" :width="718" :height="100" model="dot" :indicatorDots="false" color="#fff" :list="midadv"
					rangKey="src" imgmodel="scaleToFill" @click="i=>goPage(midadv,i)"></tm-carousel>
			</view>
			<view class="area area2">
				<view class="swipe2" @click="goLink('/pages/meet/index')">
					<tm-image :width="354" :height="457" model="aspectFill" :src="setting?.index_meet?.src"></tm-image>
				</view>
				<view class="right">
					<tm-image :width="351" :height="224" :src="setting?.index_aunt_recommend?.src"
						@click="goLink('/pages/nanny/index')"></tm-image>
					<tm-image :width="351" :height="224" :src="setting?.index_newer?.src"
						@click="goLink('/pages/welfare/index')"></tm-image>
				</view>
			</view>
			<view class="area">
				<tm-image :width="747" :height="255" :src="setting?.index_intro1?.src"></tm-image>
			</view>
			<view class="area">
				<tm-image :width="718" :height="467" :src="setting?.index_intro2?.src" @click="goApp(1)"></tm-image>
				<view class="mt-18">
					<tm-image :width="718" :height="467" :src="setting?.index_intro3?.src" @click="goApp(2)"></tm-image>
				</view>
			</view>
			<view class="card-list">
				<view class="left">
					<view class="mb-22">
						<tm-image :width="354" :height="156" :src="setting?.index_sh?.src"></tm-image>
					</view>
					<card v-for="(item,index) in cardDataLeft" :content="item" :key="'cardleft'+index"
						 @click="handleClick(item)"></card>
				</view>
				<view class="right">
					<card v-for="(item,index) in cardDataRight" :content="item" :key="'cardright'+index"
						 @click="handleClick(item)"></card>
				</view>
			</view>
			<view class="loading">
				<tm-icon spin color="#d0d0d0" :font-size="24" name="tmicon-shuaxin"></tm-icon>
				<tm-text _class="ml-20" :font-size="24" color="#d0d0d0" label="加载中"></tm-text>
			</view>
			<tabber></tabber>
		</view>
	</tm-app>
</template>
<script lang="ts" setup>
	import { ref, computed, watch, getCurrentInstance } from "vue"
	import { onShow, onLoad, onReachBottom,onPageScroll } from '@dcloudio/uni-app'
	import tmApp from "@/tmui/components/tm-app/tm-app.vue"
	import tmText from '@/tmui/components/tm-text/tm-text.vue'
	import tmImage from '@/tmui/components/tm-image/tm-image.vue'
	import tmCarousel from '@/tmui/components/tm-carousel/tm-carousel2.vue' //复制了一份，改了组件的dot样式
	import card from '@/components/card/card.vue'
	import tabber from '@/components/tabber/tabber.vue'
	import { useTmpiniaStore } from '@/tmui/tool/lib/tmpinia'
	import { useStore } from '@/until/mainpinia';
	import { share } from '@/tmui/tool/lib/share'
	import { goLink, goPage } from '@/until/index'
	import * as api from '@/api/index.js'
	import { splitArray } from './index'
	//分享功能
	const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share();
	const tmstore = useTmpiniaStore();
	onShareAppMessage();
	onShareTimeline()
	onLoad(() => {
		setTimeout(() => {
			setShareApp(tmstore.tmStore.wxshareConfig_miniMp)
			setShareTime(tmstore.tmStore.wxshareConfig_miniMp)
		}, 500)
	})
	
	// 页面安全区兼容
	const app = ref<InstanceType<typeof tmApp> | null>(null)
	const deviceInfo = uni.getSystemInfoSync();
	const statusBarHeight = deviceInfo.statusBarHeight + 'px'
	
	

	
	// 页面数据
	const store = useStore()
	const banner = computed(() => store.setting.banner)
	const menu = computed(() => store.setting.menu)
	const midadv = computed(() => store.setting.midadv)
	const setting = computed(() => store.setting)
	const cardDataLeft = ref([])
	const cardDataRight = ref([])
	const getHeight = ()=>{}
	const handleData = (data,data2) => {
		let temp1 = data.filter((item, index) => index % 2 === 0)
		let temp2 = data.filter((item, index) => index % 2 === 1)
		if(data2){
			if(data2[0]){
				temp2.unshift(data2[0])
			}
			if(data2[1]){
				temp1.splice(2,0,data2[1])
			}
		}
		cardDataLeft.value = cardDataLeft.value.concat(temp1)
		cardDataRight.value = cardDataRight.value.concat(temp2)
	}
	const page = ref(1)
	// 判断是否显示视频
	const nowvideoshow = computed(()=>store.nowvideoshow)
	const getEvaluation = async() => {
		if(store.channel&&(nowvideoshow.value===null||nowvideoshow.value===undefined)){
			watch(nowvideoshow,val=>{
				getEvaluation()
			},{once:true})
			return
		}
		
		getHeight()
		const res = await api.request.ajax({
			url: '/evaluation/index',
			type: 'POST',
			whiteList: true,
			data: {
				page: page.value,
				limit:8
			}
		})
		if (res.code !== 1) {
			return uni.showToast({ title: res.msg, icon: 'none' })
		}
		const res2 = await api.request.ajax({
			url: '/video/lists',
			type: 'POST',
			whiteList: true,
			data:{
				page:page.value,
				limit:2
			}
		})
		if (res2.code !== 1) {
			return uni.showToast({ title: res.msg, icon: 'none' })
		}
		let data = res.data||[]
		let data2 = res2.data||[]
		if(nowvideoshow.value===0){
			data2 = []
		}
		handleData(
		data.map(item=>Object.assign(item,{type:1}))
		,data2.map(item=>Object.assign(item,{type:3}))
		)
	}
	
	onLoad(() => {
		// #ifdef APP-PLUS || H5
		if (uni.getSystemInfoSync().platform == "ios") {
			uni.getNetworkType({
				success: function (res) {
					if(res.networkType!=='none'){
						getEvaluation()
					}else{
						let callback = function (res) {
							if(res.isConnected){
								getEvaluation()
								uni.offNetworkStatusChange(callback)
							}
						}
						uni.onNetworkStatusChange(callback);
					}
				},
			});
		
		}else{
			getEvaluation()
		}
		// #endif 
		// #ifndef APP-PLUS || H5
		getEvaluation()
		// #endif 
	})
	onReachBottom(() => {
		console.log('触底加载');
		page.value++
		getEvaluation()
	})	
	const goApp = (type)=>{
		store.$patch(state=>{
			state.thisAppointmentType = type
		})
		goLink('/pages/order/index')
	}
	const handleClick = (item)=>{
		if(item.type===1){
			goLink('/pages/resume/index',{hid:item.hid})
		}
		if(item.type===3){
			if(item.cate==='3'){
				goLink('/pages/classroomVideo/index',{id:item.id,navtype:3,cate:3})
			}else if(item.cate==='2'){
				goLink('/pages/classroomVideo/index',{id:item.id,navtype:3,cate:2})
			}
		}
	}
</script>
<style lang="less" scoped>
	.status_bar {
		width: 100%;
		margin-bottom: 20rpx;
	}
	.main {
		width: 750rpx;
		// height: auto;
		// min-height: 100vh;
		padding-bottom: 200rpx;
		background: #EEF0F0 linear-gradient(169deg, #FEDBDC, #EEF0F0) no-repeat center top/100% 1354rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.loading{
			width: 750rpx;
			height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.swipe {
			margin-top: 60rpx;
		}

		.button-area {
			margin-top: 38rpx;
			width: 720rpx;
			// min-height: 356rpx;
			padding-bottom: 20rpx;
			background: #FFFFFF;
			border-radius: 12rpx;

			.top-tip {
				width: 100%;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;
				padding: 22rpx 42rpx 22rpx 38rpx;

				.tip {
					display: flex;
					flex-direction: row;
					align-items: center;
				}
			}

			.button-list {
				margin-top: -30rpx;
				margin-bottom: 20rpx;
				display: flex;
				flex-direction: row;
				// justify-content: center;
				flex-wrap: wrap;
				padding: 0 24rpx;
				.item {
					display: flex;
					flex-direction: column;
					align-items: center;
					margin: 38rpx 0 0;
					width: 20%;
					height: 106rpx;
					position: relative;

					.text {
						margin-top: 12rpx;
					}
				}
			}
		}

		.area {
			margin-top: 26rpx;
		}

		.area2 {
			width: 718rpx;
			display: flex;
			flex-direction: row;
			justify-content: space-between;

			.right {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
			}
		}

		.card-list {
			margin-top: 35rpx;
			width: 750rpx;
			padding: 0 16rpx;
			display: flex;
			justify-content: space-between;
			flex-direction: row;
			align-items: flex-start;

			.left {
				width: 354rpx;
				flex-shrink: 0;
			}

			.right {
				width: 354rpx;
				flex-shrink: 0;
			}
		}
	}
</style>