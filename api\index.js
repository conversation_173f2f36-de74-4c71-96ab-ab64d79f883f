import wxRequest from './wxRequest.js'
import { useStore } from '@/until/mainpinia';
let request = new wxRequest({
	baseUrl: 'https://wx.wansao.com/client',
	// #ifdef MP-TOUTIAO
		loginUrl: '/login/dylogin',
	// #endif
	// #ifdef MP-WEIXIN
		loginUrl: '/login/login',
	// #endif
	// #ifdef MP-ALIPAY
		loginUrl: '/login/login',
	// #endif
	// 返回数据
	resolveData: function(data) {
		return data
	},
	// 拿到token存起来
	getToken: function(data) {
		// const store = useStore();
		// store.$patch((state) => {
		// 	state.userinfo = data.data
		// })
		return data.data.token
	},
	// 正常返回数据的情况
	reqSuccess: function(data) {
		if (data && data.code!==undefined&&data.code!==401) {
			return true
		}
		return false
	},
	// token过期需重新获取的情况
	reacquire: function(data) {
		if (data && [401, 402].includes(data.code)) {
			return true
		}
		return false
	}
})

const getUserInfo = ()=>{
	return request.ajax({
		url: '/user/getuserinfo',
		type: 'POST',
	}).then(res => {
		if(res.code===1){
			const store = useStore();
			store.$patch((state) => {
				state.userinfo = res.data
			})
		}else{
			uni.showToast({ title:res.msg, icon:'none' })
		}
	}).catch(err=>{
		console.error('获取用户信息失败:', err)
	})
}

function saveUserInfo(data, page) {
	return request.ajax({
		url: '/api_clone/userinfo',
		data: data,
		type: 'POST',
		whiteList: true, // 表示不需要token
		page: page
	})
}

export {
	request,
	getUserInfo,
}
