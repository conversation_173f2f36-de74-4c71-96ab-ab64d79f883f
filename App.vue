<template>
	<view></view>
</template>
<script lang="ts" setup>
	import { ref, computed, watch } from "vue"
	import { onLaunch,onShow } from '@dcloudio/uni-app'
	import * as api from '@/api/index.js'
	import { useStore } from '@/until/mainpinia';
	import { useTmpiniaStore } from '@/tmui/tool/lib/tmpinia'
	import { share } from '@/tmui/tool/lib/share'
	import app_upgrade from '@/uni_modules/app-upgrade/js_sdk/index.js'
	const { setShareApp, setShareTime,	onShareAppMessage,onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline();
	const store = useStore()
	const tmstore = useTmpiniaStore();
	onShow((e)=>{
		if(e?.query?.scene){
			store.$patch((state) => {
				state.screenScene = e.query.scene
			})
		}
	})
	const getIndex = ()=>{
		api.request.ajax({
			url: '/config/index',
			type: 'POST',
			whiteList: true,
		}).then(res => {
			if(res.code===1){
				tmstore.setWxShare(Object.assign(
					res.data.share,
					// {path:`/pages/index/index?source=share&pid=${store.userinfo.id}`}
				))
				
				store.$patch((state) => {
					state.servicephone = res.data.phone
					state.setting = res.data
				})
				setShareApp(res.data.share)
				setShareTime(res.data.share)
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	const appUpData = async ()=>{
		const res =await api.request.ajax({
			url: '/config/appversion',
			type: 'POST',
			whiteList: true,
		})
		
		let showCancel = true
		let status = 0
		let changelog = ''
		if (uni.getSystemInfoSync().platform == "ios") {
			showCancel = !res.data.version.apple_force
			status = res.data.version.apple>plus.runtime.version?1:0
			changelog = res.data.version.apple_content
		}
		if (uni.getSystemInfoSync().platform == "android") {
			showCancel = !res.data.version.android_force
			status = res.data.version.android>plus.runtime.version?1:0
			changelog = res.data.version.android_content
		}
		app_upgrade(async(versionCode)=>{
			if(res.code === 1){
				return {
					changelog,
					status, // 0 无新版本 | 1 有新版本
					path:res.data.download.android, // 新apk地址
					showCancel //是否强制更新(0是1否)
				}
			}
		},Number(!showCancel))
	}
	const miniProgramUpdata = ()=>{
		const updateManager = uni.getUpdateManager();
		
		updateManager.onCheckForUpdate(function (res) {
		  // 请求完新版本信息的回调
		  // console.log(res.hasUpdate);
		});
		
		updateManager.onUpdateReady(function (res) {
		  uni.showModal({
		    title: '更新提示',
		    content: '新版本已经准备好，是否重启应用？',
		    success(res) {
		      if (res.confirm) {
		        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
		        updateManager.applyUpdate();
		      }
		    }
		  });
		
		});
		
		updateManager.onUpdateFailed(function (res) {
		  // 新的版本下载失败
		});
	}
	onLaunch((p)=>{
		let channel = null
		// #ifdef APP-PLUS
		if (uni.getSystemInfoSync().platform == "ios") {
			channel = 'ios'
		}
		if (uni.getSystemInfoSync().platform == "android") {
			channel = plus.runtime.channel
		}
		// #endif
		// #ifdef MP-WEIXIN
		channel = 'wxxcx'
		// #endif
		store.$patch(state=>{
			state.pid = p?.query?.pid
			state.source = p?.query?.source
			state.channel = channel
		})
		const showvideo = computed(()=>store.setting.showvideo)
		watch(showvideo,val=>{
			store.$patch(state=>{
				state.nowvideoshow = val[store.channel]
			})
		})

		// #ifdef APP-PLUS || H5
		if (uni.getSystemInfoSync().platform == "ios") {
			uni.getNetworkType({
				success: function (res) {
					if(res.networkType!=='none'){
						getIndex()
						appUpData()
					}else{
						let callback = function (res) {
							if(res.isConnected){
								getIndex()
								appUpData()
								uni.offNetworkStatusChange(callback)
							}
						}
						uni.onNetworkStatusChange(callback);
					}
				},
			});

		}else{
			getIndex()
			appUpData()
		}
		// #endif 
		// #ifndef APP-PLUS || H5
		getIndex()
		api.getUserInfo()
		miniProgramUpdata()
		// #endif 
	})
</script>

<style>
	/* #ifdef APP-NVUE */
	@import './tmui/scss/nvue.css';
	/* #endif */
	/* #ifndef APP-NVUE */
	@import './tmui/scss/noNvue.css';
	/* #endif */

	/* #ifdef H5 */
	body::-webkit-scrollbar,
	div::-webkit-scrollbar,
	*::-webkit-scrollbar {
		display: none;
	}

	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}
	uni-page-body{
		height: 100% !important;
		min-height: auto !important;
	}
	body.pages-index-index uni-page-body,
	body {
		padding-bottom: 0 !important;
	}

	text {
		font-family: 'sans-serif';
	}

	/* #endif */
	
	/* #ifndef APP-NVUE */
	page,
	view,
	uni-view,
	text,
	swiper,
	swiper-item,
	image,
	navigator,
	button {
		margin: 0;
		padding: 0;
		border: none;
		box-sizing: border-box;
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}
	
	image{height:auto}
	view {
		box-sizing: border-box;
	}
	
	input,
	button,
	textarea,
	select {
		border: none;
		background: none;
	}
	
	input:focus,
	button:focus,
	select:focus {
		outline: none;
	}
	
	li {
		list-style: none;
	}
	
	a {
		text-decoration: none;
	}
	page {
		background: #fff;
		min-height: 100vh;
		font-size: 26rpx;
		font-family: "微软雅黑", "Microsoft YaHei", Verdana, Arial;
		width: 750rpx;
	}
	button::after{
		display: none;
	}
	/* #endif */

	.nomore {
		font-size: 20rpx;
		color: #999;
		position: absolute;
		bottom: 80rpx;
	}

</style>