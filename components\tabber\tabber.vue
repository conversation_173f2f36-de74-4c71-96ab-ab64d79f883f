<template>
	<view class="main2">
		<tm-tabbar :autoSelect="false" v-model:active="acc">
			<tm-tabbar-item url="" text="首页" icon="tmicon-home" activeColor="#F73030" color="#FB9E9E" fontColor="#FB9E9E" openType="switchTab" @click="goLink('/pages/index/index')"></tm-tabbar-item>
			<tm-tabbar-item url="" text="皖嫂家政" icon="tmicon-huiyuan" activeColor="#F73030" color="#FB9E9E" fontColor="#FB9E9E" openType="switchTab" @click="goLink('/pages/intr/index')"></tm-tabbar-item>
			<tm-tabbar-item url="" btn-top maintabicon="true" openType="switchTab" @click="goLink('/pages/nanny/index')"></tm-tabbar-item>
			<tm-tabbar-item text="预约阿姨" icon="tmicon-ios-filing" activeColor="#F73030" color="#FB9E9E" fontColor="#FB9E9E" openType="switchTab" @click="goLink('/pages/order/index')"></tm-tabbar-item>
			<tm-tabbar-item url="" active text="个人中心" icon="tmicon-account" activeColor="#F73030" color="#FB9E9E" fontColor="#FB9E9E" @click="goCenter"></tm-tabbar-item>
		</tm-tabbar>
	</view>
</template>
<script lang="ts" setup>
	import { ref } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import tmTabbar from '@/tmui/components/tm-tabbar/tm-tabbar.vue'
	import tmTabbarItem from '@/tmui/components/tm-tabbar-item/tm-tabbar-item2.vue'
	import * as api from '@/api/index.js'
	import { goLink } from '@/until/index'
	// import { useStore } from '@/until/mainpinia';
	// const store = useStore();
	const acc = ref(-1)
	let routeList = getCurrentPages()
	let route = routeList[routeList.length-1].route
	if(route==='pages/index/index'){
		acc.value = 0
		uni.hideTabBar()
	}else if(route==='pages/intr/index'){
		acc.value = 1
		uni.hideTabBar()
	}else if(route==='pages/order/index'){
		acc.value = 3
		uni.hideTabBar()
	}else if(route==='pages/center/index'){
		acc.value = 4
		uni.hideTabBar()
	}
	const goCenter = ()=>{
		api.getUserInfo().then(()=>{
			goLink('/pages/center/index')
		})
	}
	
</script>

<style lang="less" scoped>
	.main2{
		width: 750rpx;
		// overflow-x: hidden;
	}
</style>