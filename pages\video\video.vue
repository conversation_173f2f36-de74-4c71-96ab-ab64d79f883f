<template>
	<view>
		<button @click="start">开启视频通话</button>
		<view>在隐私协议中加上摄像头和麦克风权限</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		onLoad() {
			this.getauth();
		},
		methods: {
			getauth(){
				wx.getSetting({
				  success(res) {
					  console.log(123,res)
					//判断是否有麦克风权限
				    if (!res.authSetting['scope.record']) {
				      wx.authorize({
				        scope: 'scope.record',
				        success (ares) {
				          // 用户已经同意小程序使用录音功能，后续调用 wx.startRecord 接口不会弹窗询问
				          console.log('scope.record',ares)
				        },
						complete:function(res){
							console.log("recordcomplete："+res.errMsg)
							uni.showToast({
								icon:"none",
								title:"rc："+res.errMsg,
								duration:3000
							})
						}
				      })
				    }
					//判断是否有摄像头权限
					if (!res.authSetting['scope.camera']) {
					  wx.authorize({
					    scope: 'scope.camera',
					    success (ress) {
					      // 用户已经同意小程序使用录音功能，后续调用 wx.startRecord 接口不会弹窗询问
					      console.log('scope.camera',ress)
					    },
						complete:function(res){
							console.log("cameracomplete："+res.errMsg)
							uni.showToast({
								icon:"none",
								title:"cc："+res.errMsg,
								duration:3000
							})
						}
					  })
					}
				  }
				})
			},
			start(){
				uni.setEnable1v1Chat({
					enable:true,
					success:function(){
						console.log("success");
						uni.join1v1Chat({
							caller:{
								nickname:"发起方",
								openid:"ozAA85NyE4qhLU8LNfzqs2K1ARmY"
							},
							listener:{
								nickname:"接收方",
								openid:"ozAA85Nv58NbmVs7AQArBZXwC-7Q"
							},
							complete:function(res){
								console.log("joincomplete："+res.errMsg)
								uni.showToast({
									icon:"none",
									title:"jc："+res.errMsg,
									duration:9000
								})
							}
						})
						uni.showToast({
							icon:"none",
							title:"开启成功",
							duration:7000
						})
					},
					complete:function(res){
						console.log("complete："+res.errMsg)
						/* uni.showToast({
							icon:"none",
							title:"c："+res.errCode+res.errMsg,
							duration:5000
						}) */
					}
				})
				
			}
		}
	}
</script>

<style>

</style>
