import { defineStore} from 'pinia';

type UserInfo = Partial<{
  avatarUrl: string;
  id: string;
  identity: string;
  nickName: string;
  now_status: string;
  token: string;
  wxphone: string;
}>;
type imgObject = Partial<{
	id:string
	link:string
	src:string
	cat_idname:string
}>
type Setting = Partial<{
	aboutws:string
	banner: Array<any>
	culture: Array<any>
	center_menu : Array<any>
	honour: string
	invit_intro: string
	invit_gift_intro: string
	invite_lp: Array<any>
	invite_bgimg:string
	menu: Array<any>
	midadv: Array<any>
	news_banner: Array<any>
	phone: string
	resume_bg: string
	share: Object
	service_type:Array<any>
	service_flow:Array<any>
	service_gua:Array<any>
	area:Array<any>
	index_meet: imgObject
	index_aunt_recommend: imgObject
	index_newer: imgObject
	index_intro1: imgObject
	index_intro2: imgObject
	index_intro3: imgObject
	index_sh: imgObject
	screenTable:Object
	ck_imgs:Array<string>
	ck_form_tip:string
	aunt_order:imgObject	
	train_order:imgObject
	meets:Array<any>
	icp:string
	showvideo:Object
}>
export const useStore = defineStore('global', {
	state: () => {
		return {
			nowvideoshow:null,
			channel:null,
			screenScene:null,
			thisAppointmentType:null,
			pid:null,
			source:null,
			servicephone:null,
			userinfo:{} as UserInfo,
			setting:{
				area:[]
			} as Setting
		}
	},
	actions: {
		// setTabber(page:number) {
  		//    this.acc = page
		// },
	},
});

