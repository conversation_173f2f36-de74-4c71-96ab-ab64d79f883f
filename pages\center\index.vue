<template>
	<tm-app>
		<view class="main">
			<view class="header" style="background-image: url(imageUrlPrefix/header_center.png);">
				<view class="userinfo">
					<view class="left">
						<image :src="userinfo.avatarUrl" mode="widthFix" class="avatar"></image>
						<tm-text :label="userinfo.nickName" :fontSize="37" class="name"></tm-text>
					</view>
					<view class="right" @click="goLink('/pages/editUserInfo/index')">
						<image src="/static/img/setting.png" mode="widthFix" class="setting"></image>
						<view class="editfile">修改资料</view>
					</view>
				</view>
			</view>
			<view class="container">
				<block v-for="(item,index) in center_menu" :key="index" >
					<block v-if="item.position==0">
						<tm-cell
							v-if="item.navi_type==0"
							showAvatar
							:avatarRound="0"
							:avatarSize="40"
							:avatar="item.avatar"
							:margin="[0, 0]"
							:titleFontSize="35"
							:title="item.title"
							:rightText="item.rightText"
							@click="goLink(item.url,item.params)"
						/>
						<tm-cell
							v-else-if="item.navi_type==1"
							showAvatar
							:avatarRound="0"
							:avatarSize="40"
							:avatar="item.avatar"
							:margin="[0, 0]"
							:titleFontSize="35"
							:title="item.title"
							:rightText="item.rightText"
							@click="goApp(item.params)"
						/>
						<tm-cell
							v-else
							showAvatar
							:avatarRound="0"
							:avatarSize="40"
							:avatar="item.avatar"
							:margin="[0, 0]"
							:titleFontSize="35"
							:title="item.title"
							:rightText="item.rightText"
							@click="call"
						/>
					</block>
				</block> 				
				<!-- #ifdef MP-WEIXIN -->
					<button open-type="contact" class="contact" hover-class="none"></button>
				<!-- #endif --> 
			</view>			
			<view class="container">
				<block v-for="(item,index) in center_menu" :key="index" >
					<block v-if="item.position==1">
					<tm-cell
						v-if="item.navi_type==0"
						showAvatar
						:avatarRound="0"
						:avatarSize="40"
						:avatar="item.avatar"
						:margin="[0, 0]"
						:titleFontSize="35"
						:title="item.title"
						:rightText="item.rightText"
						@click="goLink(item.url,item.params)"
					/>
					<tm-cell
						v-else-if="item.navi_type==1"
						showAvatar
						:avatarRound="0"
						:avatarSize="40"
						:avatar="item.avatar"
						:margin="[0, 0]"
						:titleFontSize="35"
						:title="item.title"
						:rightText="item.rightText"
						@click="goApp(item.params)"
					/>
					<tm-cell
						v-else
						showAvatar
						:avatarRound="0"
						:avatarSize="40"
						:avatar="item.avatar"
						:margin="[0, 0]"
						:titleFontSize="35"
						:title="item.title"
						:rightText="item.rightText"
						@click="call"
					/>
					</block>
				</block>				
			</view>
			<!-- #ifdef APP-PLUS || H5 -->
			<view class="link_container">
				<view class="link link1" @click="openWeb('https://wx.wansao.com/statics/client/fwxy.html')">服务协议</view>
				<view class="link link1" @click="openWeb('https://wx.wansao.com/statics/client/yszc.html')">隐私政策</view>
				<view class="link" @click="appUpData">检查更新</view>
			</view>
			<!-- #endif -->
			<tabber></tabber>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { computed,ref } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import tmText from '@/tmui/components/tm-text/tm-text.vue'
	import tmCell from '@/tmui/components/tm-cell/tm-cell.vue'
	import tabber from '@/components/tabber/tabber.vue'
	import { share } from "@/tmui/tool/lib/share";
	import { useStore } from '@/until/mainpinia';
	import { goLink } from '@/until/index'
	import * as api from '@/api/index.js'
	import app_upgrade from '@/uni_modules/app-upgrade/js_sdk/index.js'
	// import { storeToRefs } from 'pinia';
	const store = useStore();
	// const {userinfo} = storeToRefs(store)
	const { onShareAppMessage, onShareTimeline } = share();
	const center_menu = computed(() => store.setting.center_menu)
	onShareAppMessage();
	onShareTimeline()
	const goApp = (type)=>{
		store.$patch(state=>{
			state.thisAppointmentType = type
		})
		goLink('/pages/order/index')
	}
	const status = ref('')
	const getStatus = ()=>{
		return api.request.ajax({
			url: '/contract/lists',
			type: 'POST',
			data: {
				latest: '1'
			}
		}).then(res=>{
			if (res.code === 1) {
				if(res.data&&res.data[0]){
					switch (res.data[0].status){
						case 0:
							status.value = '阿姨匹配中'
							break;
						case 1:
							status.value = '待上工'
							break;
						case 2:
							status.value = '上工中'
							break;
						case 3:
							status.value = '已结束'
							break;
						default:
							break;
					}
				}
			}
		})
	}
	const userinfo = computed(()=>store.userinfo)
	const call = ()=>uni.$tm.u.callPhone(store.servicephone)
	onLoad(async ()=>{
		await api.getUserInfo()
		await getStatus()
	})
	const openWeb = (url:string)=>{
		plus.runtime.openWeb(url)
	}
	const appUpData = ()=>{
		app_upgrade(async (versionCode)=>{
			//查询是否更新
			const res =await api.request.ajax({
				url: '/config/appversion',
				type: 'POST',
				whiteList: true,
			})
			let showCancel = true
			let status = 0
			let changelog = ''
			if (uni.getSystemInfoSync().platform == "ios") {
				showCancel = !res.data.version.apple_force
				status = res.data.version.apple>plus.runtime.version?1:0
				changelog = res.data.version.apple_content
			}
			if (uni.getSystemInfoSync().platform == "android") {
				showCancel = !res.data.version.android_force
				status = res.data.version.android>plus.runtime.version?1:0
				changelog = res.data.version.android_content
			}
			
			if(res.code === 1){
				if(status){
					return {
						changelog,
						status, // 0 无新版本 | 1 有新版本
						path:res.data.download.android, // 新apk地址
						showCancel //是否强制更新(0是1否)
					}
				}else{
					uni.showToast({
						title:'当前已是最新版',
						icon:'none'
					})
				}
			}
		},1)
	}
</script>

<style lang="less" scoped>
	.main{
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #F6F6F6;
		min-height: 100vh;
		.header{
			width: 750rpx;
			height: 352rpx;
			background: no-repeat center top/100% auto;
			margin-bottom: -174rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			flex-shrink: 0;
			.userinfo{
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin: 50rpx 0 0 0;
				width: 600rpx;
				.left{
					display: flex;
					align-items: center;
					.avatar{
						width: 92rpx !important;
						height: 92rpx !important;
						flex-shrink: 0;
						border: 4rpx solid #fff;
						border-radius: 50%;
					}
					.name{
						margin-left: 16rpx;
						// margin-bottom: 40rpx;
					}
				}
				.right{
					text-align: center;
					.setting{
						width: 52rpx;
					}
					.editfile{
						color: white;
						font-size: 20rpx;
					}
				}
			}
		}
		.container{
			width: 696rpx;
			margin: 34rpx 0 0;
			border-radius: 13rpx;
			padding: 20rpx 0;
			background-color: #fff;
			overflow: hidden;
			flex-shrink: 0;
			position: relative;
			.contact{
				width: 696rpx;
				height: 86rpx;
				position: absolute;
				bottom: 20rpx;
				&::after{
					display: none;
				}
			}
		}
		.link_container{
			margin-top: 40rpx;
			display: flex;
			.link{
				padding: 0 20rpx;
				color: #999;
				text-decoration: underline;
			}
			.link1{
				border-right: 1px solid #999;
			}
		}
	}

</style>