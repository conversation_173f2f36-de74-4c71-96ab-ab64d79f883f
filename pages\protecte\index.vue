<template>
	<tm-app>
		<view class="main">
			<image src="imageUrlPrefix/header2.png" class="header" mode="widthFix"></image>
			<view class="content">
				<view class="card" v-for="(item,index) in setpList" :key="item.title" :style="style">
					<view class="left">
						<image :src="'/static/img/step'+(index+1)+'.png'" class="step" mode="widthFix"></image>
					</view>
					<view class="right">
						<text class="text1">{{item.title}}</text>
						<text class="text2">{{item.desc}}</text>
					</view>
				</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref,computed } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import { share } from "@/tmui/tool/lib/share";
	import { useStore } from '@/until/mainpinia';
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const store = useStore();
	const setpList = computed(()=>store.setting.service_gua)
	const style = ref('')
	onLoad(()=>{
		setTimeout(()=>{
			style.value = 'height:auto'
		},100)
	})

</script>

<style lang="less" scoped>
	.main {
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #F2F2F2;
		.header{
			width: 750rpx;
			height: 647rpx;
		}
		.content{
			margin-top: -100rpx;
			width: 750rpx;
			min-height: 1000rpx;
			background: #FFFFFF;
			border-radius: 60rpx 60rpx 0 0;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding-top: 70rpx;
			padding-bottom: 100rpx;
			.card{
				width: 612rpx;
				height: 140rpx;
				background: url('/static/img/stepbg.png') no-repeat center top/100% 100%;
				display: flex;
				align-items: center;
				padding-right: 30rpx;
				margin-bottom: 26rpx;
				position: relative;
				.left{
					width: 100rpx;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					.step{
						width: 52rpx;
					}
				}

				.right{
					margin-left: 30rpx;
					height: 100%;
					padding: 16rpx 0;
					flex: 1;
					display: flex;
					flex-direction: column;
					color: #2E2929;
					.text1{
						font-size: 33rpx;
						font-weight: 500;
					}
					.text2{
						margin-top: 10rpx;
						font-size: 20rpx;
					}
				}
				&:before{
					width: 13rpx;
					height: 13rpx;
					background: #FF749F;
					border-radius: 50%;
					content: '';
					display: inline-block;
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					left: -30rpx;
				}
				&::after{
					width: 1px;
					height: 70%;
					background: url('/static/img/line2.png');
					content: '';
					display: inline-block;
					position: absolute;
					top: 110%;
					transform: translateY(-50%);
					left: -26rpx;
				}
				&:nth-last-child(1){
					&::after{
						display: none;
					}
				}
			}
		}
		
	}
</style>