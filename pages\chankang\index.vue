<template>
	<tm-app>
		<view class="main">
			<image :src="item" class="header" mode="widthFix" v-for="(item,index) in ck_imgs" :key="'img'+index"></image>
			<view class="form">
				<view class="ftit">填写信息</view>
				<view class="form-item">
					<view class="label">
						<view class="reqir">·</view>
						<view class="ft">姓名：</view>
					</view>
					<input type="text" v-model="form.name" />
				</view>
				<view class="form-item">
					<view class="label">
						<view class="reqir">·</view>
						<view class="ft">电话：</view>
					</view>
					<input type="tel" v-model="form.phone" />
				</view>
				<view class="tip">{{ck_form_tip}}</view>
				<view class="button" @click="submit">提交</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref,computed } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import { share } from "@/tmui/tool/lib/share";
	import { useStore } from '@/until/mainpinia';
	import * as api from '@/api/index.js'
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const store = useStore();
	const form = ref({
		name: '',
		phone: ''
	})
	const ck_imgs = computed(()=>store.setting.ck_imgs)
	const ck_form_tip = computed(()=>store.setting.ck_form_tip)
	const submit = () => {
		if (!form.value.name) return uni.showToast({
			title: '请填写姓名',
			icon: 'none'
		})
		if (!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(form.value.phone)) return uni.showToast({
			title: '请填写正确的手机号',
			icon: 'none'
		})
		api.request.ajax({
			url: '/user/auntorder',
			type: 'POST',
			data: {
				name: form.value.name,
				phone: form.value.phone,
				servicetype: '9',
				type:'1',
				puser_id:store.pid||'',
				source:store.source||'',
				record:'',
				experience:'',
			}
		}).then(res => {
			if (res.code === 1) {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateBack()
				}, 2000)
			} else {
				uni.showToast({ title: res.msg, icon: 'none' })
			}
		})
	}
</script>

<style lang="less" scoped>
	.main {
		padding-bottom: 200rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #fff;

		.header {
			width: 750rpx;
			flex-shrink: 0;

			&:nth-child(1) {
				height: 619rpx;
			}

			&:nth-child(2) {
				height: 643rpx;
			}

			&:nth-child(3) {
				height: 1459rpx;
			}
		}

		.form {
			margin-top: 30rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			.ftit{
				margin-top: 20rpx;
				font-size: 44rpx;
				font-weight: 500;
				color: #FF6091;
			}
			.form-item {
				margin-top: 50rpx;
				display: flex;
				align-items: center;
				padding-right: 40rpx;
				.label {
					display: flex;
					align-items: center;
					.reqir {
						color: #EC7F8E;
						font-size: 50rpx;
						line-height: 34rpx;
						margin-bottom: 6rpx;
					}

					.ft {
						margin-left: 10rpx;
						font-size: 34rpx;
						color: #343434;
					}
				}

				input {
					width: 480rpx;
					height: 64rpx;
					background: #F2F2F2;
					text-align: center;
				}
			}

			.tip {
				margin-top: 40rpx;
				font-size: 26rpx;
				color: #000000;
				line-height: 38rpx;
			}

			.button {
				margin-top: 40rpx;
				width: 336rpx;
				height: 84rpx;
				background: #FF6091;
				border-radius: 42rpx;
				font-size: 40rpx;
				color: #FFFFFF;
				line-height: 84rpx;
				text-align: center;
			}
		}
	}
</style>