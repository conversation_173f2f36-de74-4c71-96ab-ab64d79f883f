<template>
	<tm-app>
		<view class="main">
			<image src="imageUrlPrefix/header_xrfl.png" class="header" mode="widthFix"></image>
			<view class="form">
				<view class="ftit">填写信息</view>
				<view class="form-item">
					<view class="label">
						<view class="reqir">·</view>
						<view class="ft">姓名：</view>
					</view>
					<input type="text" v-model="form.name" />
				</view>
				<view class="form-item">
					<view class="label">
						<view class="reqir">·</view>
						<view class="ft">电话：</view>
					</view>
					<input type="tel" v-model="form.phone" />
				</view>
				<view class="button" @click="submit">领取福利</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import { share } from "@/tmui/tool/lib/share";
	import { useStore } from '@/until/mainpinia';
	import * as api from '@/api/index.js'
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const store = useStore();
	const form = ref({
		name: '',
		phone: ''
	})
	const submit = () => {
		if (!form.value.name) return uni.showToast({
			title: '请填写姓名',
			icon: 'none'
		})
		if (!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(form.value.phone)) return uni.showToast({
			title: '请填写正确的手机号',
			icon: 'none'
		})
		api.request.ajax({
			url: '/user/auntorder',
			type: 'POST',
			data: {
				name: form.value.name,
				phone: form.value.phone,
				servicetype: '9',
				type:'1',
				puser_id:store.pid||'',
				source:store.source||'',
				record:'',
				experience:'',
			}
		}).then(res => {
			if (res.code === 1) {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateBack()
				}, 2000)
			} else {
				uni.showToast({ title: res.msg, icon: 'none' })
			}
		})
	}
</script>

<style lang="less" scoped>
	.main {
		height: 100vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #fff;
		overflow-y: hidden;

		.header {
			width: 750rpx;
			flex-shrink: 0;
			height: 498rpx;
			margin-top: -100rpx;
		}

		.form {
			position: relative;
			z-index: 2;
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 750rpx;
			height: 987rpx;
			background: #FFFFFF;
			border-radius: 60rpx 60rpx 0 0;
			margin-top: -50rpx;
			.ftit{
				margin-top: 20rpx;
				font-size: 44rpx;
				font-weight: 500;
				color: #FF6091;
			}
			.form-item {
				margin-top: 50rpx;
				display: flex;
				align-items: center;
				padding-right: 40rpx;
				.label {
					display: flex;
					align-items: center;
					.reqir {
						color: #EC7F8E;
						font-size: 50rpx;
						line-height: 34rpx;
						margin-bottom: 6rpx;
					}

					.ft {
						margin-left: 10rpx;
						font-size: 34rpx;
						color: #343434;
					}
				}

				input {
					width: 480rpx;
					height: 64rpx;
					border-radius: 20rpx;
					background: #F2F2F2;
					text-align: center;
				}
			}

			.tip {
				margin-top: 40rpx;
				font-size: 26rpx;
				color: #000000;
				line-height: 38rpx;
			}

			.button {
				margin-top: 40rpx;
				height: 84rpx;
				background: #FF6091;
				border-radius: 42rpx;
				font-size: 38rpx;
				color: #FFFFFF;
				line-height: 84rpx;
				text-align: center;
				padding: 0 68rpx;
			}
		}
	}
</style>