<template>
	<tm-app>
		<view class="main">
			<view class="header" style="background-image: url(imageUrlPrefix/header_center.png);">
				<view class="userinfo">
					<view class="left">
						<image :src="userinfo.avatarUrl" mode="widthFix" class="avatar"></image>
						<tm-text :label="userinfo.nickName" :fontSize="37" class="name"></tm-text>
					</view>
				</view>
			</view>
			<view class="order_list">
				<view class="order" v-for="item in list" :key="item.cid">
					<!-- 订单状态标签 -->
					<view class="status_tag">
						<view class="status_btn" v-if="item.status===0">阿姨匹配中</view>
						<view class="status_btn" v-if="item.status===1">待上工</view>
						<view class="status_btn" v-if="item.status===2">上工中</view>
						<view class="status_btn" v-if="item.status===3">已结束</view>
						<view class="confirm_btn" v-if="item.status===1" @click="confirmwork(item)">确认上户</view>
					</view>

					<!-- 订单信息 -->
					<view class="order_info">
						<view class="info_row" v-if="item.serial_number">
							<text class="label">订单编号:</text>
							<text class="value">{{item.serial_number}}</text>
						</view>
						<view class="info_row">
							<text class="label">下单时间:</text>
							<text class="value">{{item.ordertime}}</text>
						</view>
						<view class="info_row">
							<text class="label">需要分类:</text>
							<text class="value">{{item.type}}</text>
						</view>
					</view>

					<view class="divider"></view>

					<!-- 服务信息 -->
					<view class="service_info">
						<view class="info_row">
							<text class="label">服务时间:</text>
							<text class="value">{{item.servicetime}}</text>
						</view>
						<view class="info_row">
							<text class="label">签约阿姨:</text>
							<text class="value">{{item.auntname}}</text>
						</view>
					</view>

					<view class="divider" v-if="item?.kefu?.realname"></view>

					<!-- 客服信息 -->
					<view class="kefu_info" @click="call(item?.kefu?.mobile)" v-if="item?.kefu?.realname">
						<view class="info_row">
							<text class="label">客服顾问:</text>
							<text class="value">{{item?.kefu?.realname}}</text>
							<tm-icon class="phone_icon" :font-size="34" color="#EC808D" name="tmicon-phone-fill" v-if="item?.kefu?.mobile"></tm-icon>
						</view>
						<view class="info_row">
							<text class="label">电话:</text>
							<text class="value phone">{{item?.kefu?.mobile}}</text>
						</view>
					</view>
				</view>
				<view class="order order2" v-if="!list[0]">
					<tm-icon :font-size="160" color="#E6E5E5" name="tmicon-file"></tm-icon>
					<text>暂无订单信息</text>
				</view>
			</view>
				<!-- <view class="footer">
				<view class="button">
					<image src="../../static/img/ordericon1.png" mode="aspectFit" class="img"></image>
					<view class="text">投诉反馈</view>
				</view>
				<view class="button">
					<image src="../../static/img/ordericon2.png" mode="aspectFit" class="img"></image>
					<view class="text">设置</view>
				</view>
				<view class="button">
					<image src="../../static/img/ordericon3.png" mode="aspectFit" class="img"></image>
					<view class="text">服务协议</view>
				</view>
				<view class="button">
					<image src="../../static/img/ordericon4.png" mode="aspectFit" class="img"></image>
					<view class="text">隐私协议</view>
				</view>
			</view> -->
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { computed,ref } from 'vue'
	import { onShow, onLoad,onReady,onReachBottom } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import tmText from '@/tmui/components/tm-text/tm-text.vue'
	import tmIcon from '@/tmui/components/tm-icon/tm-icon.vue'
	import { share } from "@/tmui/tool/lib/share";
	import { useStore } from '@/until/mainpinia';
	import { goLink } from '@/until/index'
	import * as api from '@/api/index.js'
	const store = useStore();
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const userinfo = computed(()=>store.userinfo)
	const call = (phone:string)=>phone&&uni.$tm.u.callPhone(phone)
	const page = ref(1)
	const list = ref([])
	const getContract = ()=>{
		api.request.ajax({
			url: '/contract/lists',
			type: 'POST',
			data: {
				page: page.value
			}
		}).then(res=>{
			if (res.code === 1) {
				if(res.data){
					list.value = list.value.concat(res.data)
				}
			}else{
				uni.showToast({ title: res.msg, icon: 'none' })
			}
		})
	}
	const confirmwork = (item)=>{
		api.request.ajax({
			url: '/contract/confirmwork',
			type: 'POST',
			data: {
				cid:item.cid
			}
		}).then(res=>{
			if (res.code === 1) {
				item.status = 2
				uni.showToast({ title: res.msg, icon: 'none' })
			}else{
				uni.showToast({ title: res.msg, icon: 'none' })
			}
		})
		
	}
	
	onLoad(()=>{
		getContract()
		page.value++
		getContract()
	})
	onReachBottom(() => {
		console.log('触底加载');
		page.value++
		getContract()
	})

</script>

<style lang="less" scoped>
	.main{
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #F6F6F6;
		min-height: 100vh;
		padding-bottom: 160rpx;
		.header{
			width: 750rpx;
			height: 352rpx;
			background: no-repeat center top/100% auto;
			display: flex;
			flex-direction: column;
			align-items: center;
			flex-shrink: 0;
			.userinfo{
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin: 50rpx 0 0 0;
				width: 600rpx;
				.left{
					display: flex;
					align-items: center;
					.avatar{
						width: 92rpx !important;
						height: 92rpx !important;
						flex-shrink: 0;
						border: 4rpx solid #fff;
						border-radius: 50%;
					}
					.name{
						margin-left: 16rpx;
						// margin-bottom: 40rpx;
					}
				}
			}
		}
		.order_list{
			margin-top: -138rpx;
			.order{
				width: 695rpx;
				background: #FFFFFF;
				border-radius: 10rpx;
				padding: 30rpx;
				margin-bottom: 20rpx;

				.status_tag{
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20rpx;

					.status_btn{
						background: #EC808D;
						color: #FFFFFF;
						padding: 8rpx 20rpx;
						border-radius: 20rpx;
						font-size: 24rpx;
						line-height: 1;
					}

					.confirm_btn{
						background: #EC808D;
						color: #FFFFFF;
						padding: 10rpx 30rpx;
						border-radius: 20rpx;
						font-size: 26rpx;
						line-height: 1;
					}
				}

				.order_info, .service_info, .kefu_info{
					.info_row{
						display: flex;
						align-items: center;
						margin-bottom: 15rpx;
						position: relative;

						.label{
							font-size: 28rpx;
							color: #666666;
							min-width: 160rpx;
						}

						.value{
							font-size: 28rpx;
							color: #303133;
							flex: 1;
						}

						.phone{
							color: #EC808D;
						}

						.phone_icon{
							margin-left: 10rpx;
						}
					}
				}

				.kefu_info{
					cursor: pointer;
				}

				.divider{
					width: 100%;
					height: 1px;
					background: #E2E2E2;
					margin: 20rpx 0;
				}
			}
		}
		.footer{
			width: 750rpx;
			height: 160rpx;
			position: fixed;
			bottom: 0;
			left: 0;
			background: #FFFFFF;
			padding: 0 48rpx;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			.button{
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 120rpx;
				.img{
					width: 50rpx;
					height: 50rpx;
				}
				.text{
					font-size: 26rpx;
					color: #606266;
					line-height: 66rpx;
				}
			}
		}
		.order2{
			height: 600rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			color: #999;
		}
	}

</style>