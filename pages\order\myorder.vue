<template>
	<tm-app>
		<view class="main">
			<view class="header" style="background-image: url(imageUrlPrefix/header_center.png);">
				<view class="userinfo">
					<view class="left">
						<image :src="userinfo.avatarUrl" mode="widthFix" class="avatar"></image>
						<tm-text :label="userinfo.nickName" :fontSize="37" class="name"></tm-text>
					</view>
				</view>
			</view>
			<view class="order_list">
				<view class="order_card" v-for="item in list" :key="item.cid">
					<!-- 卡片头部 -->
					<view class="card_header">
						<view class="status_container">
							<view class="status_dot" :class="getStatusClass(item.status)"></view>
							<text class="status_text">{{ getStatusText(item.status) }}</text>
						</view>
						<view class="order_number" v-if="item.serial_number">
							<text class="number_text">#{{item.serial_number}}</text>
						</view>
					</view>

					<!-- 服务类型卡片 -->
					<view class="service_card">
						<view class="service_icon">
							<tm-icon name="tmicon-home" :font-size="40" color="#6366F1"></tm-icon>
						</view>
						<view class="service_content">
							<text class="service_type">{{item.type}}</text>
							<text class="service_time">{{item.servicetime}}</text>
						</view>
						<view class="action_btn" v-if="item.status===1" @click="confirmwork(item)">
							<text class="btn_text">确认上户</text>
						</view>
					</view>

					<!-- 详细信息 -->
					<view class="detail_section">
						<view class="detail_item">
							<view class="detail_icon">
								<tm-icon name="tmicon-calendar" :font-size="32" color="#8B5CF6"></tm-icon>
							</view>
							<view class="detail_content">
								<text class="detail_label">下单时间</text>
								<text class="detail_value">{{item.ordertime}}</text>
							</view>
						</view>

						<view class="detail_item" v-if="item.auntname">
							<view class="detail_icon">
								<tm-icon name="tmicon-account" :font-size="32" color="#10B981"></tm-icon>
							</view>
							<view class="detail_content">
								<text class="detail_label">签约阿姨</text>
								<text class="detail_value">{{item.auntname}}</text>
							</view>
						</view>

						<view class="detail_item kefu_item" @click="call(item?.kefu?.mobile)" v-if="item?.kefu?.realname">
							<view class="detail_icon">
								<tm-icon name="tmicon-headset" :font-size="32" color="#F59E0B"></tm-icon>
							</view>
							<view class="detail_content">
								<text class="detail_label">客服顾问</text>
								<text class="detail_value">{{item?.kefu?.realname}}</text>
								<text class="detail_phone" v-if="item?.kefu?.mobile">{{item?.kefu?.mobile}}</text>
							</view>
							<view class="call_btn" v-if="item?.kefu?.mobile">
								<tm-icon name="tmicon-phone" :font-size="28" color="#FFFFFF"></tm-icon>
							</view>
						</view>
					</view>
				</view>
				<view class="order order2" v-if="!list[0]">
					<tm-icon :font-size="160" color="#E6E5E5" name="tmicon-file"></tm-icon>
					<text>暂无订单信息</text>
				</view>
			</view>
				<!-- <view class="footer">
				<view class="button">
					<image src="../../static/img/ordericon1.png" mode="aspectFit" class="img"></image>
					<view class="text">投诉反馈</view>
				</view>
				<view class="button">
					<image src="../../static/img/ordericon2.png" mode="aspectFit" class="img"></image>
					<view class="text">设置</view>
				</view>
				<view class="button">
					<image src="../../static/img/ordericon3.png" mode="aspectFit" class="img"></image>
					<view class="text">服务协议</view>
				</view>
				<view class="button">
					<image src="../../static/img/ordericon4.png" mode="aspectFit" class="img"></image>
					<view class="text">隐私协议</view>
				</view>
			</view> -->
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { computed,ref } from 'vue'
	import { onShow, onLoad,onReady,onReachBottom } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import tmText from '@/tmui/components/tm-text/tm-text.vue'
	import tmIcon from '@/tmui/components/tm-icon/tm-icon.vue'
	import { share } from "@/tmui/tool/lib/share";
	import { useStore } from '@/until/mainpinia';
	import { goLink } from '@/until/index'
	import * as api from '@/api/index.js'
	const store = useStore();
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const userinfo = computed(()=>store.userinfo)
	const call = (phone:string)=>phone&&uni.$tm.u.callPhone(phone)
	const page = ref(1)
	const list = ref([])
	const getContract = ()=>{
		api.request.ajax({
			url: '/contract/lists',
			type: 'POST',
			data: {
				page: page.value
			}
		}).then(res=>{
			if (res.code === 1) {
				if(res.data){
					list.value = list.value.concat(res.data)
				}
			}else{
				uni.showToast({ title: res.msg, icon: 'none' })
			}
		})
	}
	const confirmwork = (item)=>{
		api.request.ajax({
			url: '/contract/confirmwork',
			type: 'POST',
			data: {
				cid:item.cid
			}
		}).then(res=>{
			if (res.code === 1) {
				item.status = 2
				uni.showToast({ title: res.msg, icon: 'none' })
			}else{
				uni.showToast({ title: res.msg, icon: 'none' })
			}
		})
	}

	const getStatusText = (status) => {
		const statusMap = {
			0: '阿姨匹配中',
			1: '待上工',
			2: '服务中',
			3: '已完成'
		}
		return statusMap[status] || '未知状态'
	}

	const getStatusClass = (status) => {
		const classMap = {
			0: 'status_matching',
			1: 'status_pending',
			2: 'status_working',
			3: 'status_completed'
		}
		return classMap[status] || 'status_default'
	}
	
	onLoad(()=>{
		getContract()
		page.value++
		getContract()
	})
	onReachBottom(() => {
		console.log('触底加载');
		page.value++
		getContract()
	})

</script>

<style lang="less" scoped>
	.main{
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #F6F6F6;
		min-height: 100vh;
		padding-bottom: 160rpx;
		.header{
			width: 750rpx;
			height: 352rpx;
			background: no-repeat center top/100% auto;
			display: flex;
			flex-direction: column;
			align-items: center;
			flex-shrink: 0;
			.userinfo{
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin: 50rpx 0 0 0;
				width: 600rpx;
				.left{
					display: flex;
					align-items: center;
					.avatar{
						width: 92rpx !important;
						height: 92rpx !important;
						flex-shrink: 0;
						border: 4rpx solid #fff;
						border-radius: 50%;
					}
					.name{
						margin-left: 16rpx;
						// margin-bottom: 40rpx;
					}
				}
			}
		}
		.order_list{
			margin-top: -138rpx;
			padding: 0 20rpx;

			.order_card{
				background: #FFFFFF;
				border-radius: 24rpx;
				margin-bottom: 32rpx;
				overflow: hidden;
				box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
				border: 1px solid rgba(0, 0, 0, 0.04);

				.card_header{
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 32rpx 32rpx 24rpx;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

					.status_container{
						display: flex;
						align-items: center;

						.status_dot{
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							margin-right: 16rpx;

							&.status_matching{
								background: #F59E0B;
								box-shadow: 0 0 0 6rpx rgba(245, 158, 11, 0.2);
							}

							&.status_pending{
								background: #EF4444;
								box-shadow: 0 0 0 6rpx rgba(239, 68, 68, 0.2);
							}

							&.status_working{
								background: #10B981;
								box-shadow: 0 0 0 6rpx rgba(16, 185, 129, 0.2);
							}

							&.status_completed{
								background: #6B7280;
								box-shadow: 0 0 0 6rpx rgba(107, 114, 128, 0.2);
							}
						}

						.status_text{
							color: #FFFFFF;
							font-size: 32rpx;
							font-weight: 600;
						}
					}

					.order_number{
						.number_text{
							color: rgba(255, 255, 255, 0.8);
							font-size: 28rpx;
							font-weight: 500;
						}
					}
				}

				.service_card{
					display: flex;
					align-items: center;
					padding: 32rpx;
					background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

					.service_icon{
						width: 80rpx;
						height: 80rpx;
						background: rgba(255, 255, 255, 0.2);
						border-radius: 20rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 24rpx;
					}

					.service_content{
						flex: 1;

						.service_type{
							display: block;
							color: #FFFFFF;
							font-size: 36rpx;
							font-weight: 600;
							margin-bottom: 8rpx;
						}

						.service_time{
							display: block;
							color: rgba(255, 255, 255, 0.8);
							font-size: 28rpx;
						}
					}

					.action_btn{
						background: rgba(255, 255, 255, 0.2);
						border: 2rpx solid rgba(255, 255, 255, 0.3);
						border-radius: 50rpx;
						padding: 16rpx 32rpx;
						backdrop-filter: blur(10rpx);

						.btn_text{
							color: #FFFFFF;
							font-size: 28rpx;
							font-weight: 600;
						}
					}

					.detail_section{
						padding: 32rpx;

						.detail_item{
							display: flex;
							align-items: center;
							padding: 24rpx 0;
							border-bottom: 1px solid #F3F4F6;

							&:last-child{
								border-bottom: none;
							}

							&.kefu_item{
								cursor: pointer;
								transition: all 0.3s ease;
								border-radius: 16rpx;
								margin: 0 -16rpx;
								padding: 24rpx 16rpx;

								&:hover{
									background: #F9FAFB;
								}
							}

							.detail_icon{
								width: 64rpx;
								height: 64rpx;
								border-radius: 16rpx;
								display: flex;
								align-items: center;
								justify-content: center;
								margin-right: 24rpx;
								background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
							}

							.detail_content{
								flex: 1;

								.detail_label{
									display: block;
									color: #6B7280;
									font-size: 26rpx;
									margin-bottom: 8rpx;
								}

								.detail_value{
									display: block;
									color: #111827;
									font-size: 32rpx;
									font-weight: 500;
								}

								.detail_phone{
									display: block;
									color: #6366F1;
									font-size: 28rpx;
									margin-top: 4rpx;
								}
							}

							.call_btn{
								width: 64rpx;
								height: 64rpx;
								background: linear-gradient(135deg, #10B981 0%, #059669 100%);
								border-radius: 50%;
								display: flex;
								align-items: center;
								justify-content: center;
								box-shadow: 0 8rpx 24rpx rgba(16, 185, 129, 0.3);
							}
						}
					}
				}
			}
		}
		.footer{
			width: 750rpx;
			height: 160rpx;
			position: fixed;
			bottom: 0;
			left: 0;
			background: #FFFFFF;
			padding: 0 48rpx;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			.button{
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 120rpx;
				.img{
					width: 50rpx;
					height: 50rpx;
				}
				.text{
					font-size: 26rpx;
					color: #606266;
					line-height: 66rpx;
				}
			}
		}
		.order2{
			height: 600rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			color: #999;
		}
	}

</style>