<template>
	<view class="card">
		<div class="top">
			<view class="userinfo">
				<image :src="props.content.avatarUrl||'imageUrlPrefix/avatar.png'" class="avatar"></image>
				<view class="right">
					<text class="name">{{props.content.nickName}}</text>
					<text class="address">{{props.content.province}} {{props.content.city}}</text>
				</view>
			</view>
		</div>
		<view class="comment-text">
			<kevy-ellipsis :content="props.content.content" :rows="2" expand-text="展开" collapse-text="收起" :font-size="25"  />
		</view>
		<view class="comment-img">
			<tm-image-group>
				<tm-image 
				:width="220" 
				:height="164" 
				:margin="[6,6]"
				:round="7" 
				preview
				:src="item"
				:key="item"
				model="aspectFill" 
				v-for="item in props.content.pics"></tm-image>
			</tm-image-group>
		</view>
		<view class="button-area">
			<view class="burron-container">
				<button class="button" @click="dz" v-if="!iszan">
					<image src="../../static/img/zan.png" class="button-img"></image>
					<text class="button-text">点赞</text>
				</button>
				<button class="button" @click="dz" v-if="iszan">
					<image src="../../static/img/zan2.png" class="button-img"></image>
					<text class="button-text" style="color: #F83B3B;">已点赞</text>
				</button>
			</view>
			<view class="burron-container">
				<button class="button" open-type="share">
					<image src="../../static/img/share.png" class="button-img"></image>
					<text class="button-text">转发</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref, getCurrentInstance, } from 'vue'
	// import tmText from '@/tmui/components/tm-text/tm-text.vue'
	import kevyEllipsis from '@/components/kevy-ellipsis/kevy-ellipsis'
	import * as api from '@/api/index.js'
	const proxy = getCurrentInstance()?.proxy ?? null
	const emits = defineEmits(['click','zan'])
	const props = defineProps({
		content: {
			type: Object,
			default: () => {
				return {}
			}
		}
	})
	const iszan = ref(props.content.iszan)
	const dz = ()=>{
		api.request.ajax({
			url: '/evaluation/zan',
			type: 'POST',
			data:{eva_id:props.content.id},
		}).then(res => {
			if(res.code===1){
				iszan.value = !iszan.value
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
		// emits('zan', {id:props.content.id,iszan:iszan.value})
	}
	// const handleClick = () => {
	// 	emits('click', JSON.parse(JSON.stringify(props.content)))
	// }
</script>

<style lang="less" scoped>
	.card {
		width: 750rpx;
		background: #FFFFFF;
		margin-bottom: 22rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 24rpx;
		.top{
			width: 100%;
			.userinfo{
				display: flex;
				padding: 0 10rpx;
				.avatar{
					width: 84rpx;
					height: 84rpx;
					border-radius: 50%;
				}
				.right{
					margin-left: 16rpx;
					padding-top: 10rpx;
					display: flex;
					flex-direction: column;
					.name{
						font-size: 32rpx;
						color: #433F3F;
					}
					.address{
						margin-top: 4rpx;
						font-size: 20rpx;
						color: #433F3F;
					}
				}
			}
		}

		.comment-text{
			margin-top: 24rpx;
			width: 100%;
			padding: 0 10rpx;
		}
		.comment-img{
			width: 100%;
			display: flex;
			margin: 18rpx -12rpx 12rpx;
		}
		.button-area{
			margin-top: 34rpx;
			width: 100%;
			padding: 0 8rpx 0 50rpx;
			display: flex;
			justify-content: space-between;
			.burron-container{
				.button{
					display: flex;
					align-items: center;
					box-sizing: border-box;
					&::after{
						display: none;
					}
					.button-img{
						width: 24rpx;
						height: 24rpx;
					}
					.button-text{
						margin-left: 8rpx;
						font-size: 28rpx;
						color: #716D6D;
					}
				}
			}
		}
	}
</style>