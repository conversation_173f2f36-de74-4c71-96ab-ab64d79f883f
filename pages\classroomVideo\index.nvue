<template>
	<view>
		<!-- 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		注意：这是 H5、微信小程序界面，请勿和 new_index.nvue、index.nvue 混用
		 
		1. new_index.nvue、index.nvue这两个是App页面
		 
		2. 另外：data.js 是上一版本留下的假数据，这一版改成了 URL 请求了（如不需要可以删除，也可作为后端请求参考）
		 
		3. 请各位大神多多留手，我已经把请求内存开到最大了
		 
		4. 视频 id 切记是字符串类型 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 -->
		 <!-- #ifdef MP-WEIXIN -->
		 	<view style="position: absolute;top: 0;left: 0;width: 750rpx;z-index: 9;">
		 		<view class="status_bar" :style="{ height: statusBarHeight }"></view>
				<view style="margin-top: 20rpx;position: relative;">
					<image src="../../static/img/index/up.png" mode="widthFix" style="width: 60rpx;transform: rotate(-90deg) translateX(4rpx);position: absolute;left: 20rpx;z-index: 10;" @click="back"></image>
					<text style="color: #fff;font-size: 32rpx;text-align: center;font-weight: bold;" v-if="incomeData.cate==2">孕妈课堂</text>
					<text style="color: #fff;font-size: 32rpx;text-align: center;font-weight: bold;" v-if="incomeData.cate==3">阿姨视频</text>
				</view>
		 	</view>
		 <!-- #endif -->
		<!-- #ifdef MP -->
		<swiper :style="'width: '+ windowWidth +'px; height: '+ windowHeight +'px; background-color: #000000;'" :vertical="true" @animationfinish="animationfinish" @change="change" :current="current" :indicator-dots="false" @touchstart="mpTouchstart" @touchend="mpTouchend">
			<swiper-item v-for="(list,index) in dataList" :key="index">
				<view v-if="Math.abs(k-index)<=1" :style="'background: #fff url('+list.cover+') no-repeat center top/100% 100%;height: '+ windowHeight +'px;'">
					<view :style="'width: '+ windowWidth +'px; height: '+ windowHeight +'px;backdrop-filter: blur(40rpx);display:flex;flex-direction: column;align-items: center;justify-content: center;'">
						<!-- 
						1.v-if：用于控制视频在节点的渲染数
						2.muted的默认值是 false，代表默认是禁音视频的
						3.http-cache默认开启视频缓存
						4.poster（封面（方案一））：这里的封面默认处理存储在阿里云的视频
						5.show-loading：这里默认去掉播放转圈的标志
						v-if="Math.abs(k-index)<=1"
						 -->
						<video
						v-if="isShow"
						:id="list._id+''+index"
						:loop="true"
						:muted="list.isplay"
						:controls="false"
						:http-cache="true"
						:page-gesture="false"
						:show-fullscreen-btn="false"
						:show-loading="false"
						:show-center-play-btn="false"
						:enable-progress-gesture="false"
						:src="list.src"
						@ended="ended"
						@click="tapVideoHover(list.state,$event)"
						@timeupdate="timeupdate($event,index)"
						:style="'width: '+ '750rpx; height: '+'1334rpx; z-index: -1;'"
						:poster="list.cover"
						></video>
						<!-- 
						1.这里是封面（方案二）：这里的封面可以自定义。
						2.也在代码中做了批注，两种方案可以共存，不会相互影响。
						-->
						<image
						v-if="!list.playIng"
						:src="list.cover"
						:style="'width: '+ windowWidth +'px; height: '+ windowHeight +'px; position: absolute;'"
						mode="aspectFit"
						></image>
					</view>
					<!-- 播放状态：pause 的时候就会暂停 -->
					<view class="videoHover" @click="tapVideoHover(list.state,$event)" :style="'width: '+ windowWidth +'px; height: '+ windowHeight +'px;'">
						<image v-if="list.state=='pause'" class="playState" src="@/static/img/index/play.png"></image>
					</view>
					<view class="userInfo">
						<!-- 2.点赞 -->
						<view @click="cLike();" style="opacity: 0.9; margin-top: 17px;">
							<image v-if="list.like" src="@/static/img/index/xin.png" style="width: 40px; height: 40px; position: absolute; right: 6px;"></image>
							<image v-if="!list.like" src="@/static/img/index/xin-2.png" style="width: 40px; height: 40px; position: absolute; right: 6px;"></image>
							<text style="color: #FFFFFF; margin-top: 5px; font-size: 14px; text-align: center; margin-top: 40px; font-weight: bold;" :class="{'likeNumActive':list.like}">{{list.like_n}}</text>
						</view>
						<!-- 4.分享 -->
						<button open-type="share" :data-id="list._id" @click="share" style="opacity: 0.9; margin-top: 17px;display: flex;flex-direction: column;align-items: center;">
							<image src="@/static/img/index/share-fill.png" style="width: 40px; height: 40px; position: absolute; right: 5px;"></image>
							<text style="color: #FFFFFF; margin-top: 5px; font-size: 14px; text-align: center; font-weight: bold; margin-top: 40px;">转发</text>
						</button>
					</view>
					<!-- 最底下的文字部分 -->
					<view class="content">
						<text class="title">{{list.title}}</text>
						<text class="intro">{{list.intro}}</text>
					</view>
					<!-- 进度条 -->
					<view v-if="k === index" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" :style="'width: '+ (windowWidth - (windowWidth*0.10)) +'px; margin-left: '+ (windowWidth * 0.05) +'px; height: 40px; position: absolute; bottom: 10px;'">
						<!-- 不拖动情况下 -->
						<view>
							<!-- 1.底部背景进度条 -->
							<view :style="'width: '+ (windowWidth - (windowWidth*0.10)) +'px; margin-top: 18px; height: 5px; border-radius: 10px; background-color: #999999; opacity: 0.6;'"></view>
							<!-- 2.播放的进度条 -->
							<view v-if="!isTouch" :style="'width: '+ ((windowWidth - (windowWidth*0.10)) * progressBarPercent) +'px; position: absolute; margin-top: 18px; height: 5px; border-radius: 10px; background-color: #e6e4e7; '"></view>
							<!--  -->
							<view v-if="isTouch" :style="'width: '+ (videoStartPositon + addPositon) +'px; position: absolute; margin-top: 18px; height: 5px; border-radius: 10px; background-color: #e6e4e7; '"></view>
						</view>
					</view>
				</view>
			</swiper-item>
		</swiper>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<swiper :style="'width: '+ windowWidth +'px; height: '+ windowHeight +'px; background-color: #000000;'" :vertical="true" @animationfinish="animationfinish" @change="change" :current="current" :indicator-dots="false" :duration="duration">
			<swiper-item v-for="(list,index) in dataList" :key="index">
				<view v-if="Math.abs(k-index)<=1">
					<view>
						<!-- 
						1.v-if：用于控制视频在节点的渲染数
						2.muted的默认值是 false，代表默认是禁音视频的
						3.http-cache默认开启视频缓存
						4.poster（封面（方案一））：这里的封面默认处理存储在阿里云的视频
						5.show-loading：这里默认去掉播放转圈的标志
						v-if="Math.abs(k-index)<=1"
						 -->
						<video
						v-if="isShow"
						:id="list._id+''+index"
						:loop="true"
						:muted="list.isplay"
						:controls="false"
						:http-cache="true"
						:page-gesture="false"
						:show-fullscreen-btn="false"
						:show-loading="false"
						:show-center-play-btn="false"
						:enable-progress-gesture="false"
						:src="list.src"
						@ended="ended"
						@click="tapVideoHover(list.state,$event)"
						@timeupdate="timeupdate($event,index)"
						:style="'width: '+ windowWidth +'px; height: '+ windowHeight +'px; background-color: #000000; z-index: -1;'"
						:poster="list.cover"
						></video>
						<!-- 
						1.这里是封面（方案二）：这里的封面可以自定义。
						2.也在代码中做了批注，两种方案可以共存，不会相互影响。
						-->
						<image
						v-if="!list.playIng"
						:src="list.cover"
						:style="'width: '+ windowWidth +'px; height: '+ windowHeight +'px; position: absolute;'"
						mode="aspectFit"
						></image>
					</view>
					<!-- 播放状态：pause 的时候就会暂停 -->
					<view class="videoHover" @click="tapVideoHover(list.state,$event)" :style="'width: '+ windowWidth +'px; height: '+ windowHeight +'px;'">
						<image v-if="list.state=='pause'" class="playState" src="@/static/img/index/play.png"></image>
					</view>
					<view class="userInfo">
						<!-- 1.头像 -->
						<image @click="tozuozhe" class="userAvatar" :src="list.href" mode="aspectFill" v-if="list.href"></image>
						<!-- 2.点赞 -->
						<view @click="cLike(list.like);" style="opacity: 0.9; margin-top: 17px;">
							<image v-if="list.like" src="@/static/img/index/xin.png" style="width: 40px; height: 40px; position: absolute; right: 6px;"></image>
							<image v-if="!list.like" src="@/static/img/index/xin-2.png" style="width: 40px; height: 40px; position: absolute; right: 6px;"></image>
							<text style="color: #FFFFFF; margin-top: 5px; font-size: 14px; text-align: center; margin-top: 40px; font-weight: bold;" :class="{'likeNumActive':list.like}">点赞</text>
						</view>
						<!-- 3.预约 -->
						<view class="comment" @click="toComment(index)" style="opacity: 0.9; margin-top: 17px;">
							<image src="@/static/img/index/liaotian-2.png" style="width: 35px; height: 35px; position: absolute; right: 7px;"></image>
							<text style="color: #FFFFFF; margin-top: 5px; font-size: 14px; font-weight: bold; text-align: center; margin-top: 40px;" v-if="!list.yuyue">预约</text>
							<text style="color: #FFFFFF; margin-top: 5px; font-size: 14px; font-weight: bold; text-align: center; margin-top: 40px;" v-if="list.yuyue">已预约</text>
						</view>
						<!-- 4.分享 -->
						<view @click="share" style="opacity: 0.9; margin-top: 17px;">
							<image src="@/static/img/index/share-fill.png" style="width: 40px; height: 40px; position: absolute; right: 5px;"></image>
							<text style="color: #FFFFFF; margin-top: 5px; font-size: 14px; text-align: center; font-weight: bold; margin-top: 40px;">转发</text>
						</view>
						<view @click="dealVoice" style="margin-top: 17px;">
							<view style="width: 48px; height: 48px; border-radius: 50px; position: absolute; right: 2.5px;">
								<image :style="'width: 48px; height: 48px; border-radius: 50px; transform: rotate('+ rotates +'deg);'" src="@/static/img/index/bfq.png" mode="aspectFill"></image>
								<image v-if="showPlay" :style="'width: 30px; height: 30px; margin-top: 9px; margin-left: 9px; position: absolute; border-radius: 50px; transform: rotate('+ rotates +'deg);'" :src="list.href" mode="aspectFill"></image>
							</view>
						</view>
					</view>
					<!-- 最底下的文字部分 -->
					<view class="content">
						<text class="userName" :style="'width: '+ (windowWidth - 90) +'px;'">{{list.username}}</text><!-- i={{i}} -->
						<text class="tag">{{list.level_text}}</text>
					</view>
					<!-- 进度条 -->
					<view v-if="k === index" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" :style="'width: '+ (windowWidth - (windowWidth*0.10)) +'px; margin-left: '+ (windowWidth * 0.05) +'px; height: 40px; position: absolute; bottom: 10px;'">
						<!-- 不拖动情况下 -->
						<view>
							<!-- 1.底部背景进度条 -->
							<view :style="'width: '+ (windowWidth - (windowWidth*0.10)) +'px; margin-top: 18px; height: 5px; border-radius: 10px; background-color: #999999; opacity: 0.6;'"></view>
							<!-- 2.播放的进度条 -->
							<view v-if="!isTouch" :style="'width: '+ ((windowWidth - (windowWidth*0.10)) * progressBarPercent) +'px; position: absolute; margin-top: 18px; height: 5px; border-radius: 10px; background-color: #e6e4e7; '"></view>
							<!--  -->
							<view v-if="isTouch" :style="'width: '+ (videoStartPositon + addPositon) +'px; position: absolute; margin-top: 18px; height: 5px; border-radius: 10px; background-color: #e6e4e7; '"></view>
						</view>
					</view>
				</view>
			</swiper-item>
		</swiper>
		<!-- #endif -->
	</view>
</template>

<script>
	// import userList from '../new_index/data.js'//这个是假数据
	import * as api from '@/api/index.js'
	import { goLink } from '@/until/index'
	let audo = uni.createInnerAudioContext()
	audo.loop = true
	export default {
		data() {
			return {
				windowWidth: 0,
				windowHeight: 0,
				platform: "",
				model: "",
				deleteHeight: 0,
				dataList: [],
				k: 0,
				oldVideo: "",
				voice: "",
				timeout: "",
				current: 0,
				boxStyle:{//视频，图片封面样式🌟💗
					'height': 0,
					'width': 0,
				},
				
				videoID: "",
				isShow: false,
				
				showPlay: false,//转轮显示控制
				rotates: 0,//转轮旋转角度
				rotateTime: "",//转轮递归事件控制
				xrotats: "",
				
				mpcleartime: "",
				
				isClick: false,
				
				changeTimeout: "",
				mptime: 0,
				mpstartTime: 0,
				
				duration: 500,
				// -- 进度条相关 -- start
				videoStartPositon: 0,
				progressBarPercent: 0,
				touchStartPosition: 0,
				addPositon: 0,
				timeduration: 0,
				isTouch: false,
				// -- 进度条相关 -- end
				statusBarHeight:0,
				page:1,
				incomeData:{}
			}
		},
		components:{
			
		},
		watch:{
			async k(k,old_k){
				// console.log(k, old_k)
				this.progressBarPercent = 0;
				// #ifndef MP
				this.clearToTime();
				// #endif
				this.isShow = false
				this.dataList[old_k].playIng = false//如果视频暂停，就加载封面
				this.dataList[old_k].isplay = true
				this.dataList[old_k].state = 'pause'
				console.log('预留第' + (old_k + 1) + '个视频：' + this.dataList[old_k]._id+''+old_k)
				// 2.0版本已经去掉了下面这一句，视频不用暂停，只需要把声音禁止就行
				uni.createVideoContext(this.dataList[old_k]._id + '' + old_k,this).stop()//如果视频暂停，那么旧视频停止，这里的this.dataList[old_k]._id + '' + old_k，后面加 old_k 是为了每一个视频的 id 值不同，这样就可以大程度的避免串音问题
				console.log('已经暂停 --> 第' + (old_k + 1) + '个视频～')//提示
				this.dataList[k].state = 'play'
				this.isShow = true
				this.xrotats = setTimeout(()=>{
					this.showPlay = true;
					// #ifndef MP
					this.rotateX();
					// #endif
				},200)
				// #ifdef MP
				// 如果是小程序端
				clearTimeout(this.changeTimeout);
				this.dataList[k].isplay = false
				setTimeout(()=>{
					this.dataList[k].playIng = true
				},250)
				if(this.mptime < 0.2){
					this.changeTimeout = setTimeout(()=>{
						uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).play()
					},1400)
				} else {
					uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).play()
				}
				// #endif
				// #ifdef H5
				this.dataList[k].isplay = true
				audo.src = this.dataList[k].src
				if(this.isClick){
					setTimeout(()=>{
						if (typeof WeixinJSBridge == "undefined") {
							setTimeout(()=>{
								audo.play()
								uni.createVideoContext(this.dataList[k]._id+''+k,this).seek(0)
								uni.createVideoContext(this.dataList[k]._id+''+k,this).play()
								setTimeout(()=>{
									this.dataList[k].playIng = true
								},650)
							},500)
						} else {
							WeixinJSBridge.invoke('getNetworkType', {}, e => {
								setTimeout(()=>{
									audo.play()
									uni.createVideoContext(this.dataList[k]._id+''+k,this).seek(0)
									uni.createVideoContext(this.dataList[k]._id+''+k,this).play()
									setTimeout(()=>{
										this.dataList[k].playIng = true
									},850)
								},200)
							})
						}
					},200)
				} else {
					audo.pause()
					this.dataList[k].state = 'pause'
					uni.createVideoContext(this.dataList[k]._id+''+k,this).seek(0)
					uni.createVideoContext(this.dataList[k]._id+''+k,this).pause()
				}
				// #endif
				var p = k+1;
				// console.log('预加载第' + (p + 1) + '个视频：' + this.dataList[p]._id+''+p)
			}
		},
		onLoad(p) {
			this.incomeData = p||{}
			this.platform = uni.getSystemInfoSync().platform
			this.model = uni.getSystemInfoSync().model
			var model = this.model
			if(this.platform == 'ios' && (model !== 'iPhone6' || model !== 'iPhone6s' || model !== 'iPhone7' || model !== 'iPhone8')){
				this.deleteHeight = 0//有 tabbar的 修改这里可以改变视频高度
			}
			this.windowWidth = uni.getSystemInfoSync().windowWidth
			this.windowHeight = uni.getSystemInfoSync().windowHeight
			this.boxStyle.width = this.windowWidth + 'px'//给宽度加px
			this.boxStyle.height = this.windowHeight - this.deleteHeight;//有 tabbar的 修改这里可以改变视频高度
			this.get(p.id) //刚进入页面加载数据
			// #ifndef MP
			this.rotateX();
			// #endif
			this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px'
		},
		onShow(){
			console.log('回到前台');
			if(this.dataList.length !== 0){
				// #ifdef MP
				this.dataList[this.k].state = 'play';
				uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).play()
				// #endif
				// #ifdef H5
				if(this.isClick){
					this.dataList[this.k].state = 'play';
					uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).play()
					audo.play()
				}
				// #endif
			}
		},
		onHide(){
			// #ifdef MP
			this.dataList[this.k].state = 'pause';
			uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).pause()
			// #endif
			// #ifdef H5
			if(this.isClick){
				this.dataList[this.k].state = 'pause';
				uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).pause()
				audo.pause()
			}
			// #endif
			console.log('到后台');
		},
		onShareAppMessage(e) {
			if(e.from === 'button'){
				return {
					path:'/pages/classroomVideo/index?id='+e.target.dataset.id + '&cate='+this.incomeData.cate
				}
			}else{
				return {
					path:'/pages/classroomVideo/index?&cate='+this.incomeData.cate
				}
			}
		},
		onShareTimeline() {
			return {}
		},
		methods: {
			back(){
				uni.navigateBack({
					fail: () => {
						goLink('/pages/index/index')
					}
				});
			},
			mpTouchend(){
				this.mptime = (new Date()/1000) - this.mpstartTime;
			},
			mpTouchstart(){
				this.mpstartTime = (new Date()/1000);
			},
			dealVoice(){
				uni.showToast({
					title: '处理声音',
					icon: 'none'
				})
			},
			clearToTime(){
				//清理定时器
				for(let i=0;i<20;i++){
					clearTimeout(this.rotateTime);
					clearTimeout(this.xrotats);
					this.showPlay = false;
					this.rotates = 0;
				}
			},
			clearTime(){
				//清理定时器
				for(let i=0;i<20;i++){
					clearTimeout(this.rotateTime);
					clearTimeout(this.xrotats);
				}
			},
			rotateX(){
				// clearTimeout(this.rotateTime);
				this.rotateTime = setTimeout(()=>{
					this.rotateX();
					this.showPlay = true;
					this.rotates += 1;
				},30)
			},
			closeScrollview(){
				// 点击评论里面的叉叉，就会关闭评论
				this.$refs.pinglun.close();
			},
			ended(){
				// 1.播放当前视频结束时触发，自动切换下一个视频
				// this.current = this.k+1
			},
			// ---- 进度条相关 --- start
			touchstart(e){
				// console.log(e);
				this.isTouch = true;
				// #ifdef H5
				if(this.isClick){
					this.addPositon = 0;
					this.videoStartPositon = (this.windowWidth - (this.windowWidth*0.10)) * this.progressBarPercent;
					this.touchStartPosition = e.changedTouches[0].clientX;
				}
				// #endif
				// #ifdef MP
				this.addPositon = 0;
				this.videoStartPositon = (this.windowWidth - (this.windowWidth*0.10)) * this.progressBarPercent;
				this.touchStartPosition = e.changedTouches[0].clientX;
				// #endif
			},
			touchmove(e){
				// console.log(e);
				// #ifdef H5
				if(this.isClick){
					let num = e.changedTouches[0].clientX - this.touchStartPosition;
					if((this.videoStartPositon + num) <= (this.windowWidth - (this.windowWidth*0.10))) {
						this.addPositon = e.changedTouches[0].clientX - this.touchStartPosition;
					} else {
						this.addPositon = 0;
						this.videoStartPositon = (this.windowWidth - (this.windowWidth*0.10));
					}
				}
				// #endif
				// #ifdef MP
				let num = e.changedTouches[0].clientX - this.touchStartPosition;
				if((this.videoStartPositon + num) <= (this.windowWidth - (this.windowWidth*0.10))) {
					this.addPositon = e.changedTouches[0].clientX - this.touchStartPosition;
				} else {
					this.addPositon = 0;
					this.videoStartPositon = (this.windowWidth - (this.windowWidth*0.10));
				}
				// #endif
			},
			touchend(e){
				// #ifdef H5
				if(this.isClick){
					let per = Number( (this.videoStartPositon+this.addPositon) / (this.windowWidth - (this.windowWidth*0.10)) );
					let timeSubmit = parseInt( this.timeduration * per )
					audo.seek(timeSubmit)
					audo.play()
					uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).seek(timeSubmit)
					uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).play()
					this.dataList[this.k].state = 'play'
					setTimeout(()=>{
						this.isTouch = false;
					},500)
				}
				// #endif
				// #ifdef MP
				let per = Number( (this.videoStartPositon+this.addPositon) / (this.windowWidth - (this.windowWidth*0.10)) );
				let timeSubmit = parseInt( this.timeduration * per )
				audo.seek(timeSubmit)
				audo.play()
				uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).seek(timeSubmit)
				uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).play()
				setTimeout(()=>{
					this.isTouch = false;
				},500)
				// #endif
			},
			timeupdate(event,index){
				// 触发进度条更新
				// console.log(event,index);
				if(index === this.k){
					this.timeduration = event.detail.duration;
					this.progressBarPercent = parseFloat( Number( event.detail.currentTime / event.detail.duration ) );
				}
			},
			// ---- 进度条相关 --- ending
			//点击播放&&暂停
			tapVideoHover(state,event){
				console.log('state--',state);
				if(state=='play'||state=='continue'){
					this.dataList[this.k].state = 'pause';
				}else{
					this.dataList[this.k].state = 'continue';
				}
				if(this.dataList[this.k].state == 'continue'){
					this.isClick = true;
					this.dataList[this.k].playIng = true
					uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).play();//暂停以后继续播放
					// #ifdef MP
					this.dataList[this.k].isplay = false
					// #endif
					// #ifdef H5
					audo.play()
					// #endif
				}
				if(this.dataList[this.k].state == 'pause'){
					uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).pause();//暂停以后继续播放
					// #ifdef MP
					this.dataList[this.k].isplay = true
					// #endif
					// #ifdef H5
					audo.pause()
					// #endif
				}
			},
			change(event){
				this.k = event.detail.current
			},
			animationfinish(event){
				// 1.这里进行判断，如果是最后一个视频就进入 get() 方法加载视频进入列表
				if(this.k == this.dataList.length - 1){
					this.GET()
				}
			},
			//每一组结束时新的请求
			GET(){
				this.page++
				api.request.ajax({
					url: '/video/lists',
					type: 'POST',
					// whiteList: true,
					data:{
						page:this.page,
						...this.incomeData
					}
				}).then(res=>{
					if (res.code === 1) {
						var msg = res.data||[]
						// 2.这里把视频添加到视频列表
						for (let i = 0; i < msg.length; i++) {
							this.dataList.push(msg[i])
						}
					}else{
						uni.showToast({ title: res.msg, icon: 'none' })
					}
				})
			},
			get(id){
				// 1.这里引入后端请求数据
				// var msg = userList
				api.request.ajax({
					url: '/video/lists',
					type: 'POST',
					// whiteList: true,
					data:{
						page:this.page,
						video_id:id,
						...this.incomeData
					}
				}).then(res=>{
					if (res.code === 1) {
						if(res.data.length===0){
							uni.showModal({
								title: '提示',
								content: '暂时没有更多视频',
								confirmText:'返回',
								showCancel:false,
								success: function (res) {
									let routeList = getCurrentPages()
									if (res.confirm) {
										if(routeList.length>1){
											uni.navigateBack()
										}else{
											uni.reLaunch({
												url:'/pages/index/index'
											})
										}
									}
								}
							});
							return
						}
						this.isShow = false;
						var msg = res.data||[]
						// 2.这里把视频添加到视频列表
						for (let i = 0; i < msg.length; i++) {
							this.dataList.push(msg[i])
						}
						// 3.播放当前视频
						setTimeout(()=>{
							this.isShow = true;
							// #ifdef H5
							this.dataList[this.k].isplay = true
							// #endif
							// #ifdef MP
							// 如果是小程序端
							this.dataList[this.k].isplay = false
							this.dataList[this.k].state = 'play'
							// #endif
							this.dataList[this.k].playIng = true
							setTimeout(()=>{
								// #ifdef H5
								uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).seek(0)
								uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).pause()
								this.dataList[this.k].state = 'pause';
								audo.src = this.dataList[this.k].src;
								// #endif
								// #ifdef MP
								uni.createVideoContext(this.dataList[this.k]._id+''+this.k,this).play()
								// #endif
							},500)
						},200)
						this.videoID = this.dataList[this.k]._id
					}else{
						uni.showToast({ title: res.msg, icon: 'none' })
					}
				})
			},
			share(){
				// uni.showToast({
				// 	title: '分享',
				// 	icon: 'none'
				// })
			},
			async toComment(){
				let item = this.dataList[this.k]
				if(item.yuyue){
					return uni.showToast({ title: '您已经预约过了', icon: 'none' })
				}
				const res = await api.request.ajax({ url: '/user/getuserinfo', type: 'POST',})
				if(!(res.code===1&&res.data.wxphone)){
					return uni.showModal({
						title: '提示',
						content: '授权手机号后立即预约',
						confirmText:'去授权',
						success: function (res) {
							if (res.confirm) {
								goLink('/pages/editUserInfo/index')
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}
				const res2 = await api.request.ajax({
					url: '/user/auntorder',
					type: 'POST',
					data:{
						name:res.data.nickName,
						phone:res.data.wxphone,
						servicetype:'1',
						type:'1',
					}
				})
				if(res2.code!==1){
					return uni.showToast({ title: res2.msg, icon: 'none' })
				}
				const res3 = await api.request.ajax({
					url: '/video/zan',
					type: 'POST',
					data:{
						video_id:item._id,
						type:'2'
					}
				})
				if(res3.code!==1){
					uni.showToast({ title: res3.msg, icon: 'none' })
				}else{
					item.yuyue = !item.yuyue
				}
			},
			cLike(){
				let item = this.dataList[this.k]
				api.request.ajax({
					url: '/video/zan',
					type: 'POST',
					data:{
						video_id:item._id,
						type:'1'
					}
				}).then(res=>{
					if (res.code === 1) {
						item.like = !item.like
						if(item.like){
							item.like_n = Number(item.like_n) + 1
						}else{
							item.like_n = Number(item.like_n) - 1
						}
					}else{
						uni.showToast({ title: res.msg, icon: 'none' })
					}
				})
			}
		}
	}
</script>

<style>
	.container {background-color: #000000;}
	.item {
		/* width : 750rpx; */
		background-color: #000000;
		position: relative;
	}
	.videoHover{
		position: absolute;
		top: 0;
		left: 0;
		flex: 1;
		background-color: rgba(0,0,0,0.1);
		justify-content: center;
		align-items: center;
		
		/* border-style: dashed;
		border-color: #DD524D;
		border-width: 1px; */
	}
	.playState{
		width:  160rpx;
		height: 160rpx;
		opacity: 0.2;
	}
	.userInfo{
		position: absolute;
		bottom: 200rpx;
		right: 20rpx;
		flex-direction: column;
		width : 100rpx;
		z-index: 100;
	}
	.userAvatar{
		border-radius: 500%;
		margin-bottom: 15px;
		border-style: solid;
		border-width: 2px;
		border-color: #ffffff;
	}
	.userAvatar{
		width : 100rpx;
		height: 100rpx;
	}
	.likeIco,.shareIco,.commentIco{
		width : 60rpx;
		height: 60rpx;
		margin-top: 15px;
	}
	.likeNum,.commentNum,.shareTex{
		color: #ffffff;
		font-size: 30rpx;
		text-align: center;
		margin: 5px;
	}
	.likeNumActive{
		color: red;
	}
	.content{
	  width: 750rpx;
	  height: 300rpx;
	  z-index: 99;
	  position: absolute;
	  bottom: 0px;
	  background: linear-gradient(to bottom,rgba(0,0,0,0),rgba(0,0,0,0.6),rgba(0,0,0,0.8));
	  padding: 15rpx;
	  padding-left: 40rpx;
	  flex-direction: column;
	  justify-content: flex-start;
	  align-items: flex-start;
	  color: #ffffff;
	  /* background-color: aqua; */
	}
	.title {
	  font-size: 26rpx;
	  color: #ffffff;
	  margin-top: 80rpx;
	}
	.intro{
		margin-top: 10rpx;
		color: #ffffff;
		font-size: 34rpx;
	}
	.root{
		background-color: #000000;
	}
</style>
