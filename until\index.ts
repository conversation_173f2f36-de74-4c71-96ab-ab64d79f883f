import { useStore } from '@/until/mainpinia';
interface LinkData {
    navtype?: number;
    app_navtype?: number;
    appid?: string;
    [key: string]: string | number | boolean | undefined;
}
export const goLink = (url: string, data?: LinkData) => {
    if (!url) return;
	if(url.includes('?')){
		let temp = url.split('?')
		url = temp[0]
		let temp2 = temp[1].split('=')
		data[temp2[0]] = temp2[1]
	}
	let navtype = null
	// #ifdef APP-PLUS
	navtype = data?.app_navtype || data?.navtype || 1;
	// #endif
	// #ifndef APP-PLUS
	navtype = data?.navtype || 1;
	// #endif
    
    const queryParams = data ? flattenObjectToURLParams(data) : '';

	const next = ()=>{
		switch (navtype) {
		    case 1:
		        uni.navigateTo({
		            url: `${url}${queryParams ? '?' + queryParams : ''}`,
		            fail: () => {
		                uni.switchTab({ url });
		            }
		        });
		        break;
		    case 2:
				// #ifdef APP-PLUS
					plus.share.getServices(
						res => {
							let sweixin = null;
							for (let i in res) {
								if (res[i].id == 'weixin') {
									sweixin = res[i];
								}
							}
							//唤醒微信小程序
							if (sweixin) {
								sweixin.launchMiniProgram({
									id: 'gh_0febc35e3641',
									type: 0,
									path: 'pages/index/index'
								});
							} else {
								uni.showModal({
									title: '跳转失败',
									content: '您的手机上未安装微信',
									showCancel:false,
								});
							}
						}
					);
				// #endif
				// #ifndef APP-PLUS
				uni.navigateToMiniProgram({
				    appId: data?.appid || '',
				    url: url
				});
				// #endif
		        break;
		    case 3:
		        const finalUrl = `${url}${queryParams ? '?' + queryParams : ''}`;
		        const finalUrl2 = finalUrl.replace('/index','/index2')
		        // #ifdef APP-PLUS
		        uni.navigateTo({ url: finalUrl2 });
		        // #endif
		        // #ifndef APP-PLUS
		        uni.navigateTo({ url: finalUrl });
		        // #endif
		        break;
		    case 4:
		        uni.showModal({
		            content: '敬请期待',
		            showCancel: false
		        });
		        break;
		    default:
		        break;
		}
	}
	const checkPhoneList = [
		'/pages/resume/index',
		'/pages/nannyVideo/index',
		'/pages/classroomVideo/index'
		// '/pages/train/index',
		// '/pages/order/index'
	]
	
	if(checkPhoneList.find(item=>url.includes(item))){
		checkPhone(()=>{
			next()
		})
	}else{
		next()
	}
}

export const checkPhone = (fn?:Function)=>{
	const store = useStore()
	// #ifdef MP-WEIXIN
	if(!store.userinfo.wxphone){
		uni.showModal({
			title:'提示',
			content:'微信授权您的联系方式，将为您提供更贴心的服务',
			success: function (res) {
				let routeList = getCurrentPages()
				let route = '/' + routeList[routeList.length-1].route
				if (res.confirm) {
					uni.navigateTo({
						url:'/pages/login/phone',
						success: function(res) {
						  // 通过eventChannel向被打开页面传送数据
						  res.eventChannel.emit('frompath', { data: route })
						}
					})
				} else if (res.cancel) {
					if(fn){
						fn()
					}
				}
			}
		})
	}else{
		if(fn){
			fn()
		}
	}
	// #endif
	// #ifdef APP-PLUS
	if(fn){
		fn()
	}
	// #endif

}

export const goPage = (list : { link : string }[], index : number) => {
	goLink(list[index].link);
};

export function flattenObjectToURLParams(obj: Record<string, any>): string {
  const params: string[] = [];

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      params.push(`${key}=${value}`);
    }
  }

  return params.join('&');
}