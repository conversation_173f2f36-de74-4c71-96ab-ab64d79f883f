<template>
	<tm-app>
		<view class="main">
			<view class="content">
				<!-- <image src="/static/img/stit2.png" mode="widthFix" class="stit"></image> -->
				<view class="form">
					<image src="https://www.wansao.com/tpl/simpleboot/Public/screen/img/logo.png" class="logo"
						mode="widthFix"></image>
					<view class="radio">
						<view class="button" :class="servicetype==list[0]?'active':''" @click="servicetype=list[0]"
							v-if="table[list[0]]">预约{{table[list[0]]}}</view>
						<view class="button" :class="servicetype==list[1]?'active':''" @click="servicetype=list[1]"
							v-if="table[list[1]]">预约{{table[list[1]]}}</view>
					</view>
					<input type="text" placeholder="请输入您的电话" class="phoneInput" v-model="phone">
					<view class="submit" @click="submit" v-if="!shouquanShow">立即预约</view>
					<button type="default" class="submit" open-type="getPhoneNumber"
						@getphonenumber="decryptPhoneNumber" v-if="shouquanShow">手机号一键登陆</button>
					<view class="copyright">{{copyright}}</view>
				</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref, computed, watch, watchEffect } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import { useStore } from '@/until/mainpinia';
	import { share } from "@/tmui/tool/lib/share";
	import * as api from '@/api/index.js'
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()

	const table = ref({})
	const list = ref([])
	const store = useStore();
	const servicetype = ref(0)
	const phone = ref('');
	const shouquanShow = ref(true);
	const userinfo = computed(() => store.userinfo);
	const screenScene = computed(() => store.screenScene);
	const copyright = computed(() => store.setting.copyright);

	const init = (str : string) => {
		list.value = str.split('and')
		servicetype.value = list.value[0]
		phone.value = store.userinfo.wxphone
	}

	watch(phone, (val) => {
		shouquanShow.value = !/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(val);
	}, {
		immediate: true
	});

	watch(userinfo, (val) => {
		if (val.wxphone) {
			phone.value = val.wxphone;
		}
		if (store.setting.screenTable) {
			table.value = store.setting.screenTable;
		}
	}, {
		immediate: true
	});

	watch(screenScene, (val) => {
		init(val);
	}, {
		immediate: true
	});



	const decryptPhoneNumber = (e) => {
		console.log(e);
		if (e.detail.code) {
			api.request.ajax({
				url: '/user/savephonenew',
				type: 'POST',
				data: {
					code: e.detail.code,
				}
			}).then(res => {
				if (res.code === 1) {
					store.$patch((state) => {
						state.userinfo.wxphone = res.data.wxphone
						phone.value = res.data.wxphone
					})
				} else {
					uni.showToast({ title: res.msg, icon: 'none' })
				}
			})
		} else {

		}
	}
	const submit = () => {
		api.request.ajax({
			url: '/meet/applyPost',
			type: 'POST',
			data: {
				phone: phone.value,
				servicetype: servicetype.value
			}
		}).then(res => {
			if (res.code === 1) {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				})
				setTimeout(()=>{
					uni.redirectTo({
						url:'/pages/index/index'
					})
				},2000)
			} else {
				uni.showToast({ title: res.msg, icon: 'none' })
			}
		})
	}
</script>

<style lang="less" scoped>
	.main {
		display: flex;
		flex-direction: column;
		align-items: center;
		height: 100vh;

		.content {
			// animation: move 1s forwards;
			// margin-top: 200rpx;
			width: 750rpx;
			border-radius: 40rpx 40rpx 0 0;
			background: url(https://www.wansao.com/tpl/simpleboot/Public/screen/img/bj2.png) no-repeat center center/100% 100%;
			padding-bottom: 120rpx;
			flex: 1;
			position: relative;
			z-index: 2;
			display: flex;
			flex-direction: column;
			align-items: center;

			.form {
				height: 90%;
				margin-top: 100rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-around;
				.copyright{
					margin-top: 60rpx;
					color:#666
				}
				.logo {
					width: 207rpx;
				}

				.radio {
					margin-top: 100rpx;
					display: flex;
					align-items: center;

					.button {
						width: 313rpx;
						height: 121rpx;
						background: #FFFFFF;
						border-radius: 19rpx;
						border: 1rpx solid #3C3C3C;
						font-size: 37rpx;
						color: #3C3C3C;
						display: flex;
						justify-content: center;
						align-items: center;
						margin: 0 16.5rpx;
					}

					.active {
						background: #CB191D;
						color: #fff;
						border: none;
					}
				}

				.phoneInput {
					margin-top: 35rpx;
					width: 663rpx;
					height: 121rpx;
					background: #FFFFFF;
					border-radius: 19rpx;
					border: 1rpx solid #3C3C3C;
					font-size: 37rpx;
					padding: 0 37rpx;

					&::-webkit-input-placeholder {
						color: #D2D2D2;
					}

					&:-moz-placeholder {
						color: #D2D2D2;
					}

					&::-moz-placeholder {
						color: #D2D2D2;
					}

					&:-ms-input-placeholder {
						color: #D2D2D2;
					}

					box-sizing: border-box;
				}

				.submit {
					margin-top: 35rpx;
					width: 663rpx;
					height: 120rpx;
					background: #CB191D;
					border-radius: 60rpx;
					font-weight: bold;
					font-size: 37rpx;
					color: #FFFFFF;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}

	}
</style>