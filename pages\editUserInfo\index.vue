<template>
	<tm-app>
		<view class="main">
			<view class="form">
				<!-- #ifdef MP-WEIXIN -->
				<button class="form-item" open-type="chooseAvatar" @chooseavatar="edit">
					<text>头像</text>
					<image class="avatar" :src="avatar||userinfo.avatarUrl" mode="aspectFill"></image>
				</button>
				<!-- #endif -->
				<!-- #ifndef MP-WEIXIN -->
				<view class="form-item" @click="cropShow=true">
					<text>头像</text>
					<image class="avatar" :src="avatar||userinfo.avatarUrl" mode="aspectFill"></image>
				</view>
				<!-- #endif -->

				<view class="form-item">
					<text>昵称</text>
					<!-- #ifdef MP-WEIXIN -->
					<input type="nickname" :value="name" @change="change" />
					<!-- #endif -->
					<!-- #ifndef MP-WEIXIN -->
					<input type="text" :value="name" @input="change" />
					<!-- #endif -->
				</view>
				<view class="form-item">
					<text>手机号<text class="c9">{{userinfo.wxphone?'(已绑定，暂不支持修改)':''}}</text></text>
					<text v-if="userinfo.wxphone">{{userinfo.wxphone}}</text>
					<button v-else type="default" class="button2" open-type="getPhoneNumber"
						@getphonenumber="decryptPhoneNumber">授权手机号</button>
				</view>
				<view class="form-item" @click="show=true">
					<text>身份</text>
					<input :value="handleId(userinfo.identity)" disabled class="pn" />
				</view>
				<view class="form-item" @click="show2=true">
					<text>当前状态</text>
					<input :value="handleStatus(userinfo.now_status)" disabled class="pn" />
				</view>
				<!-- #ifdef APP-PLUS || H5 -->
				<view class="form-item">
					<text>账号状态：存续</text>
					<button class="button2" @click="logoff">注销账号</button>
				</view>
				<!-- #endif -->
				<!-- <text class="tip">昵称限2-32个字符，1个汉字为2个字符</text> -->
				<view class="button1" @click="submit">确认</view>
				<!-- #ifdef APP-PLUS || H5 -->
				<view class="button3" @click="exit">退出登录</view>
				<!-- #endif -->
			</view>
			<tm-picker
				:immediateChange="true"
				color="#e6212a" 
				v-model:show="show"
				:columns="area" 
				@confirm="e=>userinfo.identity = area[e].id" 
			></tm-picker>
			<tm-picker
				:immediateChange="true"
				color="#e6212a" 
				v-model:show="show2"
				:columns="area2" 
				@confirm="e=>userinfo.now_status = area2[e].id" 
			></tm-picker>
			<tm-overlay v-model:show="cropShow" :overlayClick="false" contentAnimation>
				<tm-cropimg @confirm="crop" @cance="cropShow=false"></tm-cropimg>
			</tm-overlay>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref,computed } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import tmPicker from '@/tmui/components/tm-picker/tm-picker.vue'
	import tmCropimg from '@/tmui/components/tm-cropimg/tm-cropimg.vue'
	import tmOverlay from '@/tmui/components/tm-overlay/tm-overlay.vue'
	import * as api from '@/api/index.js'
	import { goLink,goPage } from '@/until/index'
	import { share } from "@/tmui/tool/lib/share";
	import { useStore } from '@/until/mainpinia';
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const store = useStore();
	const userinfo = computed(()=>store.userinfo)
	const avatar = ref('')
	const name = ref('')

	onLoad(() => {
		// store.$patch((state) => {
		// 	state.userinfo.wxphone = ''
		// })
		name.value = store.userinfo.nickName
	})
	
	const decryptPhoneNumber = (e) => {
		console.log(e);
		if (e.detail.code) {
			api.request.ajax({
				url: '/user/savephonenew',
				type: 'POST',
				data:{
					code:e.detail.code,
					frompath:'/pages/editUserInfo/index'
				}
			}).then(res => {
				if(res.code===1){
					store.$patch((state) => {
						state.userinfo.wxphone = res.data.wxphone
					})
				}else{
					uni.showToast({ title:res.msg, icon:'none' })
				}
			})
		} else {

		}
	}
	const change = (e) => {
		name.value = e.detail.value
		console.log(name.value);
	}
	const edit = (e) => {
		avatar.value = 'data:image/jpg;base64,' + uni.getFileSystemManager().readFileSync(e.detail.avatarUrl, 'base64');
	}

	const area = [
		{ text: '其他', id: 0 },
		{ text: '宝妈', id: 1 },
		{ text: '宝爸', id: 2 },
	]
	const area2 = [
		{ text: '其他', id: 0 },
		{ text: '备孕中', id: 1 },
		{ text: '有喜了', id: 2 },
		{ text: '育儿中', id: 3 },
	]
	const handleId = computed(()=>{
		return p=>{
			return area[p]?.text
		}
	})
	const handleStatus = computed(p=>{
		return p =>{
			return area2[p]?.text
		}
	})
	const show = ref(false)
	const show2 = ref(false)
	const submit = () => {
		api.request.ajax({
			url: '/user/saveinfo',
			type: 'POST',
			data:{
				nickname:name.value||userinfo.value.nickName,
				avatar:avatar.value.replace(/^data:image\/\w+;base64,/, '')||userinfo.value.avatarUrl,
				identity:userinfo.value.identity,
				now_status:userinfo.value.now_status
			}
		}).then(res => {
			if(res.code===1){
				store.$patch((state) => {
					state.userinfo = res.data[0]
				})
				uni.navigateBack()
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	// #ifdef APP-PLUS || H5
	const exit = ()=>{
		api.request.clearToken()
		store.$patch((state) => {
			state.userinfo = {}
		})
		goLink('/pages/index/index')
	}
	const logoff = ()=>{
		uni.showModal({
			title: '提示',
			content: '您确定将要注销账号吗，注销后将不能再次访问本账号',
			success: function (res) {
				if (res.confirm) {
					api.request.ajax({
						url: '/user/logoff',
						type: 'POST',
						data:{}
					}).then(res => {
						console.log(333);
						if(res.code===1){
							exit()
						}else{
							uni.showToast({ title:res.msg, icon:'none' })
						}
					})
				}
				if (res.cancel) {

				}
			}
		});
	}
	// #endif 
	
	let cropShow = null
	let crop = null
	// #ifdef H5
	cropShow = ref(false)
	crop = (e)=>{
		avatar.value = e
		cropShow.value = false
	}
	// #endif
	
	// #ifdef MP-TOUTIAO
	cropShow = ref(false)
	crop = (e)=>{
		edit(e)
		cropShow.value = false
	}
	// #endif
	
	// #ifdef APP-PLUS
	cropShow = ref(false)
	crop = (e)=>{
		const reader = new plus.io.FileReader();
		// list[0].file.path 为file:// ....
		plus.io.resolveLocalFileSystemURL( e, entry => {
			entry.file(file => {
				reader.onloadend = function(evt) {
					console.log('evt',evt);
					avatar.value = evt.target.result
					cropShow.value = false
				}
				reader.readAsDataURL(file);
			}, function ( err ) {
				console.log( err.message );
			} );
		}, err => {
			console.log(err)
		} );
	}
	// #endif
	
	
	
</script>

<style lang="less" scoped>
	.main {
		display: flex;
		flex-direction: column;
		align-items: center;
		height: 100vh;
		background-color: #fff;

		.form {
			flex: 1;
			margin-top: 20rpx;
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;

			.form-item {
				background-color: #fff;
				width: 90%;
				height: 100rpx;
				border-bottom: 1px solid #f6f6f6;
				padding: 10rpx 0;
				display: flex;
				justify-content: space-between;
				align-items: center;

				&:after {
					display: none;
				}

				.avatar {
					border-radius: 50%;
					object-fit: cover;
					width: 80rpx;
					height: 80rpx;
				}

				text {
					font-size: 26rpx;
					color: #333;

					.c9 {
						color: #999;
					}
				}

				input {
					text-align: right;
					flex: 1;
					font-size: 26rpx;
					color: #333;
				}
				.pn{
					pointer-events: none;
				}
			}
		}

		.tip {
			width: 100%;
			color: #999;
			font-size: 24rpx;
			margin-top: 10rpx;
			padding-left: 30rpx;
		}

		.button1{
			margin-top: 120rpx;
			width: 473rpx;
			height: 79rpx;
			background: linear-gradient(-16deg, #DF2121, #E6443C);
			border-radius: 37rpx;
			font-size: 41rpx;
			line-height: 79rpx;
			color: #FFFFFF !important;
			text-align: center;
			letter-spacing: 4rpx;
		}
		.button3{
			margin-top: 60rpx;
			width: 473rpx;
			height: 79rpx;
			background: #999;
			border-radius: 37rpx;
			font-size: 41rpx;
			line-height: 79rpx;
			color: #fff !important;
			text-align: center;
			letter-spacing: 4rpx;
		}
		
		.button2{
			margin: 0;
			width: 200rpx;
			height: 60rpx;
			line-height: 60rpx;
			border-radius: 30rpx;
			font-size: 28rpx;
			font-weight: 500;
			background: #E6443C !important;
			color: #fff !important;
			text-align: center;
		}
	}
</style>