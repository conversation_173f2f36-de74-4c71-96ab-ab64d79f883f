<template>
	<tm-app>
		<view class="main">
			<view class="card" v-for="item in list" :key="item.id">
				<view class="title">{{item.name}}</view>
				<view class="address">
					<image src="/static/img/addricon.png" mode="widthFix" class="addricon"></image>
					<text class="text">{{item.address}}</text>
				</view>
				<image src="/static/img/line.png" class="line" v-if="item.imgs[0]"></image>
				<view class="photo-wall" v-if="item.imgs[0]">
					<image :src="item2" class="storephoto" v-for="item2 in item.imgs" :key="item2"></image>
				</view>
				<image src="/static/img/navi.png" class="navi" @click="navi(item)"></image>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import { share } from "@/tmui/tool/lib/share";
	import * as api from '@/api/index.js'
	import { goLink,goPage } from '@/until/index'
	import { navTo } from '@/until/map'
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	onLoad(() => {
		getData()
	})
	const list = ref([])
	const getData = ()=>{
		api.request.ajax({
			url: '/store/lists',
			type: 'POST',
			whiteList: true,
		}).then(res => {
			if(res.code===1){
				list.value = res.data
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	const navi = item =>{
		uni.openLocation({
			latitude:Number(item.latitude),
			longitude:Number(item.longitude),
			name:item.name,
			address:item.address
		})
		// #ifdef APP-PLUS
			let point = {
				lat: Number(item.latitude), // gcj02  
				lng: Number(item.longitude), // gcj02  
				lbl: item.name, // label  
				dtl: item.address // detail  
			}
			navTo(point,'amap')
		// #endif
	}

</script>

<style lang="less" scoped>
	.main {
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #F6F6F6;
		padding-bottom: 200rpx;
		.card{
			margin-top: 40rpx;
			width: 717rpx;
			// min-height: 342rpx;
			background: url('/static/img/storebg.jpg') no-repeat center top/100% 100%;
			border-radius: 20rpx;
			padding: 45rpx 30rpx 25rpx;
			position: relative;
			color: #181411;
			.title{
				width: 100%;
				padding-left: 26rpx;
				font-size: 34rpx;
			}
			.address{
				margin-top: 20rpx;
				padding-left: 26rpx;
				display: flex;
				.addricon{
					margin-top: 6rpx;
					width: 18rpx;
				}
				.text{
					margin-left: 10rpx;
					font-size: 24rpx;
					max-width: 440rpx;
				}
			}
			.line{
				width: 100%;
				height: 1px;
			}
			.photo-wall{
				margin-top: 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.storephoto{
					width: 136rpx;
					height: 136rpx;
					border-radius: 10rpx;
				}
			}
			.navi{
				width: 65rpx;
				height: 65rpx;
				position: absolute;
				top: 68rpx;
				right: 95rpx;
			}
		}
	}
</style>