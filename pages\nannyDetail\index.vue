<template>
	<tm-app ref="app">
		<view class="main" v-if="detail?.name">
			<view class="swipe" v-if="detail?.banner[0]">
				<tm-carousel :autoplay="false" :round="0" :width="750" :height="815" :list="detail.banner" rangKey="imgpath"></tm-carousel>
			</view>
			<view class="area area1">
				<view class="top" v-if="detail?.name">
					<text class="name">{{detail.name}}</text>
					<view class="nanny-title" v-for="item in detail.job" :key="item">{{item}}</view>
				</view>
				<view class="bottom">
					<view class="tag">{{detail?.age}}</view>
					<view class="tag">{{detail?.area}}</view>
					<view class="tag" v-if="detail?.workyear">从业{{detail.workyear}}年</view>
				</view>
			</view>
			<view class="area" v-if="detail?.intro">
				<view class="stit stit1">个人介绍</view>
				<view class="desc">
					<kevy-ellipsis :content="detail.intro" :rows="6" :font-size="26" ref="ke1"  />
				</view>
				<view class="more" @click="showContent" v-if="!show1">
					<text>查看完整</text>
					<tm-icon rotate :rotate-deg="0" :font-size="20" color="#909399" name="tmicon-angle-down"></tm-icon>
				</view>
				<view class="more" @click="showContent" v-if="show1">
					<text>收起全部</text>
					<tm-icon rotate :rotate-deg="0" :font-size="20" color="#909399" name="tmicon-angle-up"></tm-icon>
				</view>
			</view>
			<view class="area" v-if="detail.evaluation.content">
				<view class="stit stit2">老师评价</view>
				<view class="desc">
					<kevy-ellipsis :content="detail.evaluation.content" :rows="6" :font-size="26" ref="ke2"  />
				</view>
				<view class="more" @click="showContent2" v-if="!show2">
					<text>查看完整</text>
					<tm-icon rotate :rotate-deg="0" :font-size="20" color="#909399" name="tmicon-angle-down"></tm-icon>
				</view>
				<view class="more" @click="showContent2" v-if="show2">
					<text>收起全部</text>
					<tm-icon rotate :rotate-deg="0" :font-size="20" color="#909399" name="tmicon-angle-up"></tm-icon>
				</view>
			</view>
			<view class="area area2">
				<view class="tabs">
					<tm-tabs @change="tabschange" showTabsLineAni :list="tabsTitle" :item-width="127" :width="635"
						activeFontColor="#FF6091" tabs-line-ani-color="#FF6091" v-model:active-name="activeName">
					</tm-tabs>
				</view>
				<view class="tab-pane" v-if="detail?.certData[0]" id="pos1">
					<view v-for="item in detail.certData" :key="item.id">
						<tm-cell 
						showAvatar 
						:avatarRound="0" 
						:margin="[0 ,8]" 
						:titleFontSize="28" 
						bottomBorder
						:title="item.desc" 
						:avatarSize="30" 
						:avatar="item.pic"
						:rightIcon="item.show?'tmicon-angle-right':''"
						@click="previewImgs(item.imgs)"
						/>
					</view>
				</view>
			</view>
			<view class="area area3" id="pos2">
				<view class="tagList" v-if="detail?.skillsInfo[0]">
					<view class="left">
						<view v-for="(item,index) in detail.skillsInfo" :key="item">
							<view class="tag" v-if="index%2===1">{{item}}</view>
						</view>
					</view>
					<view class="right">
						<view v-for="(item,index) in detail.skillsInfo" :key="item">
							<view class="tag" v-if="index%2===0">{{item}}</view>
						</view>
					</view>
				</view>
			</view>
			<view class="area" id="pos3">
				<view class="photo-wall" v-if="detail?.photos[0]">
					<tm-image-group>
						<tm-image 
						:width="150" 
						:height="150" 
						:margin="[6,6]" 
						:round="6" 
						preview 
						:previewList="detail.photos"
						previewName="file_path"
						:src="item.imgpath"
						:key="item.imgpath"
						model="aspectFill" 
						v-for="item in detail.photos"></tm-image>
					</tm-image-group>
				</view>
			</view>
			<view class="area" id="pos4">
				<view class="charts" v-if="detail?.workingDate[0]">
					<text class="tip">注意：标红的为该月安排的天数</text>
					<view class="list">
						<view class="line" v-for="(item,index) in detail.workingDate" :key="index">
							<view class="bar">
								<view class="container">
									<tm-progress :width="304" :height="22" color="#FF6091"
										v-model:percent="item.percent"></tm-progress>
								</view>
							</view>
							<text class="day">{{item.days}}</text>
							<text class="month">{{item.month}}月</text>
						</view>
					</view>
				</view>
			</view>
			<view id="pos5">
				<comment-card :content="item" v-for="item in evaluation" :key="item.id" />
			</view>
			<view class="button1" @click="goLink('/pages/moreComment/index',{hid})">查看更多</view>
			<view class="area">
				<view class="stit stit3">联系方式</view>
				<view class="desc2">安徽省皖嫂家政服务平台由省妇联创办于2001年</view>
				<view class="tel-area" @click="call">
					<image src="../../static/img/tel.png" class="tel-icon"></image>
					<text class="telphone">{{store.servicephone}}</text>
					<text class="tip">点击拨打电话免费咨询</text>
				</view>
			</view>
			<view class="area">
				<view class="stit stit3">温馨提示</view>
				<view class="desc2">
					<text>如您需要预约阿姨，请联系皖嫂家政工作人员，并与公司签订正规服务合同，以免除您后顾之忧。\n皖嫂家政对此拥有最终解释权。</text>
				</view>
			</view>
			<view class="float">
				<view class="left">
					<view class="container" @click="goLink('/pages/index/index')">
						<image src="../../static/img/home.png" class="icon"></image>
						<text class="text">首页</text>
					</view>
					<button class="container" open-type="share" :data-detail="detail">
						<image src="../../static/img/share2.png" class="icon"></image>
						<text class="text">分享</text>
					</button>
				</view>
				<view class="right">
					<view class="button button2" @click="call">电话咨询</view>
					<view class="button button3" @click="goApp(1)">立即预约</view>
				</view>
			</view>
		</view>
	</tm-app>
</template>
<script lang="ts" setup>
	import { ref, computed,getCurrentInstance } from "vue"
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from "@/tmui/components/tm-app/tm-app.vue"
	import tmTabs from '@/tmui/components/tm-tabs/tm-tabs.vue'
	import tmCell from '@/tmui/components/tm-cell/tm-cell.vue'
	import tmImage from '@/tmui/components/tm-image/tm-image2.vue'
	import tmProgress from '@/tmui/components/tm-progress/tm-progress.vue'
	import tmCarousel from '@/tmui/components/tm-carousel/tm-carousel2.vue' //复制了一份，改了组件的dot样式
	import * as api from '@/api/index.js'
	import { goLink } from '@/until/index'
	import { useStore } from '@/until/mainpinia';
	import commentCard from '@/components/commentCard/commentCard.vue'
	import kevyEllipsis from '@/components/kevy-ellipsis/kevy-ellipsis'
	// import { share } from "@/tmui/tool/lib/share";
	// const { onShareAppMessage, onShareTimeline } = share();
	// onShareAppMessage();
	// onShareTimeline()
	
	// 分享功能
	import { onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
	const shareFunction = ({from,target})=>{
		if(from==='button'&&target?.dataset?.detail?.name){
			return {
				title:target.dataset.detail.job[0]+target.dataset.detail.name+'的个人简历',
				imageUrl:target.dataset.detail.banner[0],
				desc:'我在查看'+target.dataset.detail.name+'的个人简历',
				path: `/pages/nannyDetail/index?hid=${target.dataset.detail.hid}&source=resume&pid=${store.userinfo.id}`,
			}
		}else{
			return {
				title:'',
				imageUrl:'',
				desc:''
			}
		}
	}
	onShareAppMessage(shareFunction)
	onShareTimeline(shareFunction)
	
	// 页面数据
	const store = useStore()
	const detail = ref(null)
	const hid = ref('')
	onLoad((p)=>{
		hid.value = p.hid
		getData(p.hid)
		getEvaluation(p.hid)
	})
	const getData = (hid:string)=>{
		api.request.ajax({
			url: '/aunt/detail',
			type: 'POST',
			whiteList: true,
			data:{hid}
		}).then(res => {
			if(res.code===1){
				detail.value = res.data
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	const evaluation = ref([])
	const getEvaluation = (hid:string)=>{
		api.request.ajax({
			url: '/evaluation/index',
			type: 'POST',
			whiteList: true,
			data:{hid}
		}).then(res => {
			if(res.code===1){
				evaluation.value = res.data
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	// 个人介绍和老师评价切换展示
	const ke1 = ref(null)
	const show1 = ref(false)
	const showContent = ()=>{
		ke1.value.changeCollapse()
		show1.value = !show1.value
	}
	const ke2 = ref(null)
	const show2 = ref(false)
	const showContent2 = ()=>{
		ke2.value.changeCollapse()
		show2.value = !show2.value
	}
	
	// tab功能
	const tabsTitle = ref([
		{ key: '1', title: '认证', },
		{ key: '2', title: '技能', },
		{ key: '3', title: '相册', },
		{ key: '4', title: '档期', },
		{ key: '5', title: '评价', }
	])
	const activeName = ref(0)
	// #ifndef APP-PLUS || H5
	const app2 = getCurrentInstance()
	const SelectorQuery = uni.createSelectorQuery().in(app2)
	SelectorQuery.select(`#pos1`).boundingClientRect(data=>{
		if(data?.top>0){
			activeName.value = 1
		}
	})
	const getHeight = ()=>{
		SelectorQuery.exec()
	}
	setInterval(()=>{
		getHeight()
	},1000)
	// #endif 
	const tabschange = (e)=>{
		uni.pageScrollTo({
			selector:'#pos'+e
		})
	}
	// 电话和预览
	const call = ()=>uni.$tm.u.callPhone(store.servicephone)
	const previewImgs = imgs => uni.previewImage({
		urls:imgs,
	})
	const goApp = (type)=>{
		store.$patch(state=>{
			state.thisAppointmentType = type
		})
		goLink('/pages/order/index')
	}
</script>
<style lang="less" scoped>
	.main {
		width: 750rpx;
		padding-bottom: 200rpx;
		background: #F4F3F3;
		display: flex;
		flex-direction: column;
		align-items: center;
		.swipe{
			width: 750rpx;
			height: 815rpx;
		}
		.area {
			width: 750rpx;
			overflow-x: hidden;
			min-height: 210rpx;
			background: #FFFFFF;
			position: relative;
			padding: 50rpx 64rpx 46rpx;
			margin-bottom: 14rpx;
			color: #373232;

			.top {
				display: flex;
				flex-direction: row;
				align-items: center;

				.name {
					font-size: 50rpx;
					font-weight: 600;
					color: #151515;
				}

				.nanny-title {
					margin-left: 24rpx;
					width: 200rpx;
					height: 44rpx;
					background: url('/static/img/nanny-title.png') no-repeat center top/100% 100%;
					font-size: 24rpx;
					color: #FFFFFF;
					line-height: 44rpx;
					padding-left: 50rpx;
				}
			}

			.bottom {
				margin-top: 30rpx;
				display: flex;
				flex-direction: row;
				align-items: center;

				.tag {
					width: 142rpx;
					height: 38rpx;
					border: 1px solid rgba(67, 67, 67, 0.2);
					border-radius: 19rpx;
					font-size: 25rpx;
					font-weight: 300;
					color: #373232;
					line-height: 38rpx;
					text-align: center;
					margin-right: 20rpx;
				}
			}

			.stit {
				font-size: 36rpx;
				position: relative;
				margin-left: 14rpx;
				font-weight: 500;
			}

			.stit1 {
				&:before {
					width: 6rpx;
					height: 32rpx;
					background: #FF5F91;
					border-radius: 3rpx;
					content: '';
					position: absolute;
					top: 52%;
					transform: translateY(-50%);
					left: -14rpx;
				}
			}

			.stit2 {
				&:before {
					width: 6rpx;
					height: 32rpx;
					background: #72BDFE;
					border-radius: 3rpx;
					content: '';
					position: absolute;
					top: 52%;
					transform: translateY(-50%);
					left: -14rpx;
				}
			}

			.stit3 {
				&:before {
					width: 6rpx;
					height: 27rpx;
					background: #F83B3B;
					border-radius: 3rpx;
					content: '';
					position: absolute;
					top: 52%;
					transform: translateY(-50%);
					left: -18rpx;
				}

				margin-left: 18rpx;
				font-size: 30rpx;
				color: #F83B3B;
			}

			.desc {
				margin-top: 20rpx;
				font-size: 26rpx;
				line-height: 44rpx;
				text-align: justify;
			}

			.desc2 {
				width: 100%;
				margin-top: 20rpx;
				font-size: 26rpx;
				color: #606266;
			}

			.tel-area {
				margin-top: 20rpx;
				width: 100%;
				display: flex;
				align-items: center;

				.tel-icon {
					width: 36rpx;
					height: 36rpx;
					border-radius: 50%;
				}

				.telphone {
					font-size: 40rpx;
					color: #606266;
					margin-left: 10rpx;
				}

				.tip {
					font-size: 26rpx;
					color: #F83B3B;
					margin-left: 10rpx;
				}
			}

			.more {
				margin-top: 16rpx;
				margin-bottom: -20rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				text {
					font-size: 25rpx;
					font-weight: 300;
					color: #909399;
					margin-right: 8rpx;
				}
			}

			.tagList {
				width: 100%;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				.left {
					width: 48%;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
				}

				.right {
					width: 48%;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
				}

				.tag {
					border: 1px solid rgba(67, 67, 67, 0.2);
					border-radius: 21rpx;
					padding: 0 14rpx;
					margin-bottom: 22rpx;
					font-size: 22rpx;
					color: #373232;
					line-height: 42rpx;
				}

			}

			.tab-pane {
				width: 635rpx;
			}

			.photo-wall {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				width: 660rpx;
				margin-left: -14rpx;
			}

			.charts {
				width: 635rpx;
				padding-top: 80rpx;
				position: relative;

				.tip {
					position: absolute;
					right: 0;
					top: 0;
					font-size: 24rpx;
					color: #6B6363;
				}

				.list {
					width: 100%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;

					.line {
						display: flex;
						flex-direction: column;
						align-items: center;
						width: 40rpx;

						.bar {
							height: 304rpx;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;

							.container {
								width: 304rpx;
								transform: rotate(270deg);
							}
						}

						.day {
							margin-top: 6rpx;
							font-size: 22rpx;
							line-height: 30rpx;
							font-weight: 400;
							color: #888481;
							white-space: nowrap;
						}

						.month {
							margin-top: 6rpx;
							line-height: 30rpx;
							font-size: 22rpx;
							color: #6B6363;
							white-space: nowrap;
						}
					}
				}
			}
		}

		.area1 {
			margin-top: -54rpx;
			border-radius: 60rpx 60rpx 0 0;

		}

		.area2 {
			padding: 36rpx 0;
			display: flex;
			flex-direction: column;
			align-items: center;

		}

		.area3 {
			padding: 40rpx 40rpx;

		}

		.button1 {
			width: 458rpx;
			height: 84rpx;
			background: linear-gradient(180deg, #F95959, #F73030);
			border-radius: 42rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin: 20rpx 0 40rpx;
			font-size: 36rpx;
			color: #FFFFFF;
			line-height: 84px;
			letter-spacing: 4rpx;
		}

		.float {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 750rpx;
			background: rgba(255, 255, 255, .9);
			backdrop-filter: blur(8rpx);
			box-shadow: 1rpx 1rpx 16rpx 0rpx rgba(194, 186, 186, 0.5);
			padding: 20rpx 60rpx 20rpx 78rpx;
			padding-bottom: 0;
			padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left {
				width: 150rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.container {
					display: flex;
					flex-direction: column;
					align-items: center;

					&:after {
						display: none;
					}

					.icon {
						width: 30rpx;
						height: 30rpx;
					}

					.text {
						width: 44rpx;
						height: 30rpx;
						line-height: 30rpx;
						margin-top: 10rpx;
						font-size: 22rpx;
						color: #413D3E;
					}
				}
			}

			.right {
				width: 400rpx;
				height: 72rpx;
				border: 1rpx solid #F83B3B;
				border-radius: 36rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				overflow: hidden;

				.button {
					font-size: 36rpx;
					height: 70rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					flex: 1;
				}

				.button2 {
					background-color: #fff;
					color: #F83B3B;
				}

				.button3 {
					background-color: #F83B3B;
					color: #fff;
				}
			}
		}
	}
</style>