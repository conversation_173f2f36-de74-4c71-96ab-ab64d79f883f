<template>
	<tm-app ref="app">
		<view class="main">
			<view class="normal-pad border-shadow">
				<view class="pad-content">
					<view class="p-content" v-for="(item,index) in evaluations" :key="index">
						<view class="pj-title">
							<view> {{item.nickName}}评价:</view>
							<view>共<text class="day">{{item.days}}</text>天</view>
						</view>
						<view class="show-p">
							{{item.content}}
						</view>
						<view class="item-xc">
							<image :src="item2" @click='previewImgs(item.pics,item2)' class="item-xc-img" mode="aspectFill"
								v-for="(item2,index2) in item.pics" :key="index2"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
	</tm-app>
</template>
<script lang="ts" setup>
	import { ref, computed, getCurrentInstance } from "vue"
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from "@/tmui/components/tm-app/tm-app.vue"
	import * as api from '@/api/index.js'
	import { useStore } from '@/until/mainpinia';
	import { share } from "@/tmui/tool/lib/share";
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	
	// 页面数据
	const store = useStore()
	const evaluations = ref([])
	const hid = ref('')
	onLoad((p) => {
		hid.value = p.hid
		getData(hid.value)
	})
	const getData = (hid : string) => {
		api.request.ajax({
			url: '/aunt/auntEvaluations',
			type: 'POST',
			whiteList: true,
			data: { hid }
		}).then(res => {
			if (res.code === 1) {
				evaluations.value = res.data.list
			} else {
				uni.showToast({ title: res.msg, icon: 'none' })
			}
		})
	}
	const previewImgs = (imgs : string[],current:string) => uni.previewImage({
		urls: imgs,
		current:current
	})
</script>
<style lang="less" scoped>
	.main {
		width: 750rpx;
		padding-bottom: 100rpx;
		background: #fff;
		display: flex;
		flex-direction: column;
		align-items: center;

		.normal-pad {
			width: 690rpx;
			background: #fff;
			border-radius: 30rpx;
			margin: 30rpx 30rpx 0 30rpx;
			box-shadow: 0 0 10rpx #d3d3d3;
			border-radius: 30rpx;

			.pad-content {
				padding: 30rpx;

				.p-content {
					padding: 20rpx 0;
					line-height: 40rpx;

					.pj-title {
						display: flex;
						justify-content: space-between;
						color: #999;

						.day {
							color: red
						}
					}

					.show-p {
						padding: 20rpx 0 30rpx;
						font-size: 26rpx;
					}

					.item-xc {
						width: 100%;
						display: flex;
						align-items: center;
						flex-wrap: wrap;
						margin: 0 -10rpx;

						.item-xc-img {
							width: 136rpx;
							height: 136rpx;
							border-radius: 20rpx;
							margin: 10rpx;
						}
					}

				}

			}
		}
	}
</style>