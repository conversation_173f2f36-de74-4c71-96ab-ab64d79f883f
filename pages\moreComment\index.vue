<template>
	<tm-app ref="app">
		<view class="main" v-if="evaluation[0]">
			<comment-card :content="item" v-for="item in evaluation" :key="item.id" />
			<view class="nomore" v-if="nomore">没有更多了</view>
		</view>
	</tm-app>
</template>
<script lang="ts" setup>
	import { ref, computed } from "vue"
	import { onShow, onLoad, onReachBottom } from '@dcloudio/uni-app'
	import tmApp from "@/tmui/components/tm-app/tm-app.vue"
	import commentCard from '@/components/commentCard/commentCard.vue'
	import { share } from "@/tmui/tool/lib/share";
	import * as api from '@/api/index.js'
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const page = ref(1)
	const hid = ref('')
	const nomore = ref(false)
	onLoad((p)=>{
		hid.value = p.hid
		getEvaluation()
	})
	onReachBottom(()=>{
		page.value++
		getEvaluation()
	})
	const evaluation = ref([])
	const getEvaluation = ()=>{
		api.request.ajax({
			url: '/evaluation/index',
			type: 'POST',
			whiteList: true,
			data:{
				hid:hid.value,
				page:page.value
			}
		}).then(res => {
			if(res.code===1){
				if(res.data[0]){
					evaluation.value = evaluation.value.concat(res.data)
				}else{
					nomore.value = true
				}
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
		
	}
</script>
<style lang="less" scoped>
	.main {
		width: 750rpx;
		padding-bottom: 200rpx;
		background: #F2F2F2;
		padding-top: 22rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
	}
</style>