<template>
	<tm-app>
		<view class="main">
			<image :src="store.setting.meets[0]['src']" class="header" mode="widthFix"></image>
			<view class="content" v-if="detail?.title">
				<view class="card">
					<image :src="store.setting.meets[1]['src']" class="meettit" mode="widthFix"></image>
					<view class="p1">{{detail.title}}</view>
					<view class="p2">
						<image src="/static/img/addricon1.png" mode="widthFix" class="addricon"></image>
						<text>{{detail.address}}</text>
					</view>
					<view class="p2">
						<image src="/static/img/addricon2.png" mode="widthFix" class="addricon"></image>
						<text>{{detail.traffic}}</text>
					</view>
					<view class="p2">
						<image src="/static/img/addricon3.png" mode="widthFix" class="addricon"></image>
						<text>活动时间：{{detail.date}}</text>
					</view>
				</view>
				<view class="card">
					<view class="content2">
						<tm-html :content="detail.content" :tag-style="tagStyle" :container-style="htmlStyle" v-if="htmlStyle"></tm-html>
					</view>
				</view>
				<view class="card">
					<image src="/static/img/stit2.png" mode="widthFix" class="stit"></image>
					<view class="form">
						<view class="form-item">
							<view class="label">
								<text class="reqir">*</text>
								<text class="ft">姓名：</text>
							</view>
							<input type="text" v-model="form.name" />
						</view>
						<view class="form-item">
							<view class="label">
								<text class="reqir">*</text>
								<text class="ft">电话：</text>
							</view>
							<input type="tel" v-model="form.phone" />
						</view>
						<view class="tip">皖嫂家政保障您的隐私</view>
						<view class="button" @click="submit">提交</view>
					</view>
				</view>
				<view v-if="list[0]">
					<view class="card" v-for="item in list" >
						<image src="/static/img/stit3.png" mode="widthFix" class="stit"></image>
						<view class="list">
							<view class="box" v-for="(item,index) in list" :key="item.id" @click="goLink(item.gzhurl)">
								<image :src="item.thumb" mode="aspectFill" class="main-img"></image>
								<text>{{item.title}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- <back-top :scroll-top="scrollTop"></back-top> -->
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref } from 'vue'
	import { onShow, onLoad,onPageScroll } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import { share } from "@/tmui/tool/lib/share";
	import { useStore } from '@/until/mainpinia';
	import backTop from '@/components/backTop/backTop.vue'
	import * as api from '@/api/index.js'
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	onLoad(()=>{
		getList()
	})
	const store = useStore();
	const list = ref([])
	const detail = ref(null)
	const getList = async()=>{
		const res = await api.request.ajax({
			url: '/meet/detail',
			type: 'POST',
			whiteList: true,
		})
		const {data} = await api.request.ajax({
			url: '/meet/lists',
			type: 'POST',
			whiteList: true,
			data:{
				except_id:res.data.id
			}
		})
		list.value = data||[]

		if(res.code===1){
			detail.value = res.data
		}
	}
	const tagStyle = ref({
		image:'width:100%',
		img:'width:100%',
		table:'width:100%',
	})
	const htmlStyle = ref('font-size: 30rpx;line-height: 60rpx;color:#666;white-space: normal;')
	const form = ref({
		name:'',
		phone:''
	})
	const submit = ()=>{
		if(!form.value.name)return uni.showToast({
			title:'请填写姓名',
			icon:'none'
		})
		if(!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(form.value.phone))return uni.showToast({
			title:'请填写正确的手机号',
			icon:'none'
		})
		api.request.ajax({
			url: '/user/auntorder',
			type: 'POST',
			data:{
				name:form.value.name,
				phone:form.value.phone,
				servicetype:'1',
				type:'1',
				puser_id:store.pid||'',
				source:store.source||'',
				record:'',
				experience:'',
				frompath:'pages/meet/index'
			}
		}).then(res=>{
			if(res.code===1){
				uni.showToast({
					title:res.msg,
					icon:'none'
				})
				setTimeout(()=>{
					uni.navigateBack()
				},2000)
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	const goLink = (url)=>{
		uni.navigateTo({
		  url: '/pages/web/index',
		  success: function(res) {
		    // 通过eventChannel向被打开页面传送数据
		    res.eventChannel.emit('webLink', { data: url })
		  }
		})
	}
	const scrollTop = ref(0)
	onPageScroll((e)=>{
		scrollTop.value = e.scrollTop
	})
</script>

<style lang="less" scoped>
	.main {
		padding-bottom: 200rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #EC808D;

		.header {
			width: 750rpx;
			height: 1137rpx;
			flex-shrink: 0;
		}

		.content {
			margin-top: -100rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			.card {
				width: 698rpx;
				min-height: 380rpx;
				background: linear-gradient(0deg, #FFE7EA, #FFFFFF);
				box-shadow: 0rpx 8rpx 6rpx 2rpx rgba(242, 145, 161, 0.55);
				border-radius: 14rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				position: relative;
				padding: 38rpx 40rpx;
				margin-bottom: 60rpx;
				.content2{
					width: 618rpx;
				}
				.meettit {
					width: 404rpx;
				}
				.p1{
					margin-top: 30rpx;
					width: 100%;
					text-align: center;
					color: #353131;
					font-size: 38rpx;
					font-weight: 500;
				}
				.p2{
					margin-top: 20rpx;
					width: 100%;
					// padding-left: 40rpx;
					display:flex;
					.addricon{
						margin-top: 10rpx;
						width: 24rpx;
						flex-shrink: 0;
					}
					text{
						margin-left: 14rpx;
						font-size: 28rpx;
						color: #454242;
						line-height: 44rpx;
					}
				}
				.stit{
					width: 560rpx;
					position: absolute;
					top: -20rpx;
				}
				.form{
					margin-top: 30rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					.form-item{
						margin-top: 50rpx;
						display: flex;
						align-items: center;
						.label{
							.reqir{
								color: #D84040;
							}
							.ft{
								margin-left: 10rpx;
								font-size: 34rpx;
								color: #343434;
							}
						}
						input{
							margin-left: 20rpx;
							width: 420rpx;
							height: 72rpx;
							background: #EEE8E8;
							border-radius: 14rpx;
							text-align: center;
						}
					}
					.tip{
						margin-top: 60rpx;
						font-size: 22rpx;
						color: #5D5555;
						line-height: 42rpx;
					}
					.button{
						margin-top: 15rpx;
						width: 510rpx;
						height: 98rpx;
						background: url(/static/img/button-bg.png) no-repeat center top/100% 100%;
						font-size: 40rpx;
						color: #FFFFFF;
						line-height: 80rpx;
						text-align: center;
					}
				}
				.list{
					margin-top: 30rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					.box{
						margin-top: 40rpx;
						width: 100%;
						display: flex;
						flex-direction: column;
						align-items: center;
						border-bottom: 1px solid #000000;;
						.main-img{
							width: 640rpx;
							height: 270rpx;
						}
						text{
							margin-top: 30rpx;
							margin-bottom: 20rpx;
							width: 640rpx;
							font-size: 27rpx;
							color: #322D2D;
						}
						&:nth-last-child(1){
							border-bottom: none;
						}
					}
				}
			}
		}

	}
</style>