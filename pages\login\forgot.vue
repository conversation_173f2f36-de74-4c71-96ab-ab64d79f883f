<template>
	<tm-app>
		<view class="main">
			<image src="/static/img/loginbg.svg" mode="widthFix" class="waves"></image>
			<view class="logo-area">
				<view class="logo-bg"></view>
				<image src="/static/img/logo.png" mode="widthFix" class="logo"></image>
			</view>
			<view class="nav">
				<view class="nav-item">找回密码</view>
			</view>
			<view class="content">
				<view class="form">
					<input type="tel" placeholder="请输入手机号" placeholder-style="color:#909399" v-model="form.phone" />
					<input type="password" placeholder="请输入新密码" placeholder-style="color:#909399" v-model="form.password" />
					<view class="yzm_area">
						<input type="text" placeholder="请输入验证码" placeholder-style="color:#909399" v-model="form.yzm" />
						<view class="yzm_button" v-if="!time" @click="sendmsg">获取</view>
						<view class="yzm_button yhq" v-if="time">
							{{time}}s
						</view>
					</view>
				</view>
				<view class="button" @click="submit">登录</view>

			</view>
			<image-code v-model:show="showImageCode" @success="onImageCodeSuccess"></image-code>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import {ref} from 'vue'
	import * as api from '@/api/index.js'
	import { goLink } from '@/until/index'
	import ImageCode from '@/components/handleSendCode/ImageCode.vue'
	import { useSmsCode } from '@/composables/useSmsCode'

	const form = ref({
		phone:'',
		password:'',
		yzm:''
	})
	const { time, showImageCode, handleSendCode, handleImageCodeSuccess } = useSmsCode()

	const sendmsg = () => {
		handleSendCode(form.value.phone)
	}

	const onImageCodeSuccess = async (code: string) => {
		await handleImageCodeSuccess(form.value.phone, code)
	}

	const submit = ()=>{
		if(!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(form.value.phone))return uni.showToast({
			title:'请填写正确的手机号',
			icon:'none'
		})
		if(form.value.password.length<6)return uni.showToast({
			title:'密码至少6位',
			icon:'none'
		})
		if(!form.value.yzm)return uni.showToast({
			title:'请填写验证码',
			icon:'none'
		})
		api.request.ajax({
			url: '/login/resetpass',
			type: 'POST',
			whiteList: true,
			data:{
				phone:form.value.phone,
				password:form.value.password,
				yzm:form.value.yzm,
			}
		}).then(res => {
			if(res.code===1){
				uni.setStorageSync('token', res.data.token);
				// #ifdef H5
				window.location.reload()
				// #endif
				// #ifndef H5
				goLink('/pages/index/index')
				// #endif
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
</script>

<style lang="less" scoped>
	@import url(index.less);
	.main{
		.content{
			padding-bottom: 200rpx;
		}
	}
</style>