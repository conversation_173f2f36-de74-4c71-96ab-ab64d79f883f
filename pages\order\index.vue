<template>
	<tm-app>
		<view class="main">
			<image :src="setting?.aunt_order?.src" class="header" mode="widthFix"></image>
			<view class="content">
				<!-- <image src="/static/img/stit2.png" mode="widthFix" class="stit"></image> -->
				<view class="form">
					<view class="form-item" @click="showdateq2=true">
						<view class="label">
							<text class="ft">服务类型：</text>
						</view>
						<input type="text" class="pn" v-model="form.service_type_str" readonly disabled />
					</view>
					<view class="form-item" @click="showdateq=true">
						<view class="label">
							<text class="ft">所在区域：</text>
						</view>
						<input type="text" class="pn" v-model="form.areaStr" readonly disabled />
					</view>
					<view class="form-item">
						<view class="label">
							<text class="ft">您的姓名：</text>
						</view>
						<input type="text" v-model="form.name" />
					</view>
					<view class="form-item">
						<view class="label">
							<text class="ft">您的电话：</text>
						</view>
						<input type="tel" v-model="form.phone" />
					</view>
					<view class="bottom">
						<view class="tip">皖嫂家政保障您的隐私</view>
						<view class="button" @click="submit">提交</view>
					</view>
				</view>
			</view>
			<tm-picker 
				v-model:model-str="form.areaStr"
				:immediateChange="true"
				color="#e6212a" 
				v-model:show="showdateq" 
				:columns="area" 
				@confirm="e=>form.area = area[e].id" 
			></tm-picker>
			<tm-picker 
				color="#e6212a" 
				:immediateChange="true"
				v-model:show="showdateq2" 
				v-model:model-str="form.service_type_str"
				:columns="service_type" 
				@confirm="e => form.service_type = service_type[e].id" 
			></tm-picker>
			<tabber></tabber>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref,computed } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import tmPicker from '@/tmui/components/tm-picker/tm-picker.vue' //tm-drawer第25行设置了100vh高度
	import tabber from '@/components/tabber/tabber.vue'
	import { useStore } from '@/until/mainpinia';
	import { share } from "@/tmui/tool/lib/share";
	import * as api from '@/api/index.js'
	import { checkPhone } from '@/until/index'
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const store = useStore();
	const service_type = computed(()=>store?.setting?.service_type?.map(item=>Object.assign(item,{text:item.name})))
	const area = computed(()=>store?.setting?.area?.map(item=>Object.assign(item,{text:item.name})))
	const setting = computed(() => store.setting)
	const showdateq = ref(false)
	const showdateq2 = ref(false)
	const form = ref({
		name:null,
		phone:null,
		areaStr:'庐阳区',
		area:'2225',
		service_type_str:'月嫂',
		service_type:'1',
	})
	onShow(()=>{
		checkPhone()
		setThisAppointmentType()
	})
	const setThisAppointmentType = ()=>{
		if(store.thisAppointmentType){
			form.value.service_type = store.thisAppointmentType
			form.value.service_type_str = service_type.value.find((item)=>item.id===form.value.service_type).name
			store.$patch(state=>{
				state.thisAppointmentType = null
			})
		}
	}
	const submit = ()=>{
		if(!form.value.service_type)return uni.showToast({
			title:'请选择服务类型',
			icon:'none'
		})
		if(!form.value.area)return uni.showToast({
			title:'请选择所在区域',
			icon:'none'
		})
		if(!form.value.name)return uni.showToast({
			title:'请填写姓名',
			icon:'none'
		})
		if(!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(form.value.phone))return uni.showToast({
			title:'请填写正确的手机号',
			icon:'none'
		})
		api.request.ajax({
			url: '/user/auntorder',
			type: 'POST',
			data:{
				name:form.value.name,
				phone:form.value.phone,
				area:form.value.area,
				servicetype:form.value.service_type,
				type:'1',
				puser_id:store.pid||'',
				source:store.source||'',
				record:'',
				experience:'',
			}
		}).then(res=>{
			if(res.code===1){
				uni.showToast({
					title:res.msg,
					icon:'none'
				})
				form.value = {
					name:null,
					phone:null,
					areaStr:'庐阳区',
					area:'2225',
					service_type_str:'月嫂',
					service_type:'1',
				}
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
</script>

<style lang="less" scoped>
	.main {
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #e6212a;
		min-height: 100vh;
		.header {
			width: 750rpx;
			height: 831rpx;
			flex-shrink: 0;
		}

		.content {
			// animation: move 1s forwards;
			// margin-top: 200rpx;
			width: 750rpx;
			border-radius: 40rpx 40rpx 0 0;
			background: linear-gradient(0deg, #FFE7EA, #FFFFFF);
			box-shadow: 0rpx 8rpx 6rpx 2rpx rgba(242, 145, 161, 0.55);
			padding-bottom: 220rpx;
			flex: 1;
			position: relative;
			z-index: 2;
			display: flex;
			flex-direction: column;
			align-items: center;
				.stit{
					width: 560rpx;
					position: absolute;
					top: -20rpx;
				}
				.form{
					height: 100%;
					margin-top: 30rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					
					.form-item{
						margin-top: 50rpx;
						margin-right: 40rpx;
						display: flex;
						align-items: center;
						.label{
							width: 200rpx;
							text-align: right;
							.reqir{
								color: #D84040;
								margin-right: 10rpx;
							}
							.ft{

								font-size: 34rpx;
								color: #343434;
							}
						}
						input{
							margin-left: 20rpx;
							width: 420rpx;
							height: 72rpx;
							background: #EEE8E8;
							border-radius: 14rpx;
							text-align: center;
						}
						.pn{
							pointer-events: none;
						}
					}
					.bottom{
						flex: 1;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
					}
					.tip{
						margin-top: 60rpx;
						font-size: 22rpx;
						color: #5D5555;
						line-height: 42rpx;
					}
					.button{
						margin-top: 15rpx;
						width: 510rpx;
						height: 98rpx;
						background: url(/static/img/button-bg.png) no-repeat center top/100% 100%;
						font-size: 40rpx;
						color: #FFFFFF;
						line-height: 80rpx;
						text-align: center;
					}
				}
		}

	}
</style>