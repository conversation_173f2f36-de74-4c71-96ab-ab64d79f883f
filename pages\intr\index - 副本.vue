<template>
	<tm-app>
		<view class="main">
			<view class="swipe">
				<tm-carousel autoplay :round="3" :width="716" :height="368" model="dot" color="#FCD9DB"
					:list="listimg" rangKey="src" @click="i=>goPage(listimg,i)"></tm-carousel>
			</view>
			<view class="tabs">
				<tm-tabs @change="tabschange" showTabsLineAni :list="tabsTitle" :item-width="240" :width="750"
					default-name="1" tabs-line-ani-color="#F83B3B" activeFontColor="#F83B3B"></tm-tabs>
			</view>
			<view class="area" v-if="type===1">
				<view class="detail">
					<!-- <image src="/static/img/detailbg.png" class="detailbg"></image> -->
					<view class="stit">
						<image src="/static/img/stit.png" mode="widthFix" class="stitbg"></image>
						<tm-text :font-size="30" color="#F01010" class="text" label="皖嫂简介"></tm-text>
					</view>
					<view class="content">
						<tm-html :content="aboutws" :tag-style="tagStyle" :container-style="htmlStyle" scroll-table></tm-html>
					</view>
				</view>
			</view>
			<!-- <view class="area" v-if="type===2">
				<view class="detail">
					<view class="stit">
						<image src="/static/img/stit.png" mode="widthFix" class="stitbg"></image>
						<tm-text :font-size="30" color="#F01010" class="text" label="企业文化"></tm-text>
					</view>
					<view class="content">
						<tm-html :content="culture" :tag-style="tagStyle" :container-style="htmlStyle" scroll-table></tm-html>
					</view>
				</view>
			</view> -->
			<view class="area" v-if="type===2">
				<view class="detail detail2">
					<view class="stit">
						<image src="/static/img/stit.png" mode="widthFix" class="stitbg"></image>
						<tm-text :font-size="30" color="#F01010" class="text" label="皖嫂门店"></tm-text>
					</view>
					<view class="card" v-for="item in list" :key="item.id">
						<view class="title">{{item.name}}</view>
						<view class="address">
							<image src="/static/img/addricon.png" mode="widthFix" class="addricon"></image>
							<text class="text">{{item.address}}</text>
						</view>
						<view class="address" v-if="item.tel_1" @click="call(item.tel_1)">
							<image src="/static/img/cellicon7.png" mode="widthFix" class="addricon"></image>
							<text class="text">月嫂热线：{{item.tel_1}}</text>
						</view>
						<view class="address" v-if="item.tel_2" @click="call(item.tel_2)">
							<image src="/static/img/cellicon6.png" mode="widthFix" class="addricon"></image>
							<text class="text">家政热线：{{item.tel_2}}</text>
						</view>
						<view class="address" v-if="item.tel_3" @click="call(item.tel_3)">
							<image src="/static/img/cellicon2.png" mode="widthFix" class="addricon"></image>
							<text class="text">保洁热线：{{item.tel_3}}</text>
						</view>
						<view class="address" v-if="item.tel_4" @click="call(item.tel_4)">
							<image src="/static/img/cellicon5.png" mode="widthFix" class="addricon"></image>
							<text class="text">产康热线：{{item.tel_4}}</text>
						</view>
						<image src="/static/img/line.png" class="line" v-if="item.imgs[0]"></image>
						<view class="photo-wall" v-if="item.imgs[0]">
							<image :src="item2" class="storephoto" v-for="item2 in item.imgs" :key="item2"></image>
						</view>
						<image src="/static/img/navi.png" class="navi" @click="navi(item)"></image>
					</view>
				</view>
			</view>
			
			<view class="area" v-if="type===3">
				<view class="detail detail3">
					<image src="/static/img/detailbg3.png" class="detailbg"></image>
					<view class="stit">
						<image src="/static/img/stit.png" mode="widthFix" class="stitbg"></image>
						<tm-text :font-size="30" color="#F01010" class="text" label="企业荣誉"></tm-text>
					</view>
<!-- 					<view class="content">
						<view class="photo-wall">
							<image :src="item" class="photo" @click="preview(index)" v-for="(item,index) in photoList" :key="item"></image>
						</view>
					</view> -->
					<view class="content">
						<tm-html :content="honour" :tag-style="tagStyle" :container-style="htmlStyle" scroll-table></tm-html>
					</view>
				</view>
			</view>
			<view class="area" v-if="type===4">
				<view class="detail">
					<image src="/static/img/detailbg2.png" class="detailbg"></image>
					<view class="stit">
						<image src="/static/img/stit.png" mode="widthFix" class="stitbg"></image>
						<tm-text :font-size="30" color="#F01010" class="text" label="皖嫂资讯"></tm-text>
					</view>
					<view class="content">
						<view class="ul">
							<view class="li" v-for="item in newsList" :key="item.id" @click="goLink('/pages/newsDetail/index',{id:item.id})">
								<view class="left"><view class="text">{{item.title}}</view></view>
								<view class="right"><view class="text">{{item.date}}</view></view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<tabber></tabber>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref,computed, watch } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import tmText from '@/tmui/components/tm-text/tm-text.vue'
	import tmCarousel from '@/tmui/components/tm-carousel/tm-carousel2.vue' //复制了一份，改了组件的dot样式
	import tabber from '@/components/tabber/tabber.vue'
	import tmTabs from '@/tmui/components/tm-tabs/tm-tabs.vue'
	import tmHtml from '@/tmui/components/tm-html/tm-html.vue'
	import { useStore } from '@/until/mainpinia';
	import * as api from '@/api/index.js'
	import { goLink,goPage } from '@/until/index'
	import { navTo } from '@/until/map'
	import { share } from "@/tmui/tool/lib/share";
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage()
	onShareTimeline()
	
	const type = ref(1)
	
	const store = useStore()
	const tabsTitle = ref([
		{ key: '1', title: '皖嫂简介', },
		{ key: '2', title: '皖嫂门店', },
		// { key: '3', title: '企业荣誉', },
		{ key: '4', title: '皖嫂资讯', }
	])
	const tagStyle = {
		image:'max-width:100%',
		text:'font-size: 30rpx;line-height: 40rpx',
		view:'font-size: 30rpx;line-height: 40rpx',
		table:'max-width:100%'
	}
	const htmlStyle = 'padding: 20rpx;;width:750rpx'
	
	
	const listimg = computed(()=>store.setting.news_banner)
	const aboutws = computed(()=>store.setting.aboutws)
	const culture = computed(()=>store.setting.culture)
	const honour = computed(()=>store.setting.honour)
	const photoList = [
		'imageUrlPrefix/certificate1.jpg',
		'imageUrlPrefix/certificate2.jpg',
		'imageUrlPrefix/certificate3.jpg',
		'imageUrlPrefix/certificate4.jpg',
		'imageUrlPrefix/certificate5.jpg',
		'imageUrlPrefix/certificate6.jpg',
		'imageUrlPrefix/certificate7.jpg',
		'imageUrlPrefix/certificate8.jpg',
		'imageUrlPrefix/certificate9.jpg',
	]
	
	const newsList = ref([])
	const preview = (count:number) => uni.previewImage({urls:photoList,current:count,loop:true})
	const list = ref([])
	const getData = ()=>{
		api.request.ajax({
			url: '/news/lists',
			type: 'POST',
			whiteList: true,
		}).then(res => {
			if(res.code===1){
				newsList.value = res.data
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
		api.request.ajax({
			url: '/store/lists',
			type: 'POST',
			whiteList: true,
		}).then(res => {
			if(res.code===1){
				list.value = res.data
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	const navi = item =>{
		uni.openLocation({
			latitude:Number(item.latitude),
			longitude:Number(item.longitude),
			name:item.name,
			address:item.address
		})
		// #ifdef APP-PLUS
			let point = {
				lat: Number(item.latitude), // gcj02  
				lng: Number(item.longitude), // gcj02  
				lbl: item.name, // label  
				dtl: item.address // detail  
			}
			navTo(point,'amap')
		// #endif
	}
	const call = (tel) => uni.$tm.u.callPhone(tel)
	onLoad(() => {
		getData()
	})
	const tabschange = (e:string) =>{
		type.value = Number(e)
	}
</script>

<style lang="less" scoped>
	.main {
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #F6F6F6;
		padding-bottom: 200rpx;

		.swipe {
			margin-top: 14rpx;
		}

		.tabs {
			width: 750rpx;
			overflow-x: hidden;
			margin-top: 44rpx;
		}
		
		.area{
			width: 750rpx;
			overflow-x: hidden;
			position: relative;
			display: flex;
			flex-direction: column;
			align-items: center;
			.detail {
				margin-top: 16rpx;
				width: 750rpx;
				min-height: 929rpx;
				background-color: #fff;
				position: relative;
				display: flex;
				flex-direction: column;
				align-items: center;
				.detailbg {
					width: 100%;
					height: 100%;
					position: absolute;
					top: 0;
					left: 0;
				}
			
				.stit {
					position: absolute;
					top:6rpx;
					z-index: 2;
					width: 233rpx;
					height: 81rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					.stitbg {
						width: 100%;
						height: 100%;
						position: absolute;
						top: 0;
						left: 0;
					}
					.text{
						position: relative;
						z-index: 2;
						margin:16rpx 60rpx 0 0
					}
				}
				.content{
					position: relative;
					z-index: 2;
					padding: 136rpx 52rpx 100rpx;
					width: 750rpx;
					.ul{
						width: 650rpx;
						color: #6B6666;
						.li{
							/* display: flex; */
							align-items: center;
							justify-content: space-between;
							padding-left: 16rpx;
							margin-bottom: 20rpx;
							.left{
								font-size: 30rpx;
								line-height: 70rpx;
								position: relative;
								/* width: 450rpx; */
								width: 100%;
								.text{
									white-space:nowrap;
									overflow:hidden;
									text-overflow:ellipsis;
								}
								&:before{
									content: '';
									display: inline-block;
									width: 10rpx;
									height: 10rpx;
									background: #EC808D;
									border-radius: 50%;
									position: absolute;
									left: -16rpx;
									top: 50%;
									transform: translateY(-50%);
								}
							}
							.right{
								text-align: right;
								font-size: 30rpx;
								font-weight: 300;
								line-height: 70rpx;
								white-space:nowrap;
							}
						}
					}
					.photo-wall{
						width: 720rpx;
						display: flex;
						align-items: center;
						flex-wrap: wrap;
						.photo{
							width: 210rpx;
							height: 154rpx;
							margin: 8rpx 14rpx;
							
						}
					}
				}

			}
			.detail3{
				min-height: auto;
				height: 731rpx;
			}
			.detail2{
				background-color: transparent;
				display: flex;
				flex-direction: column;
				align-items: center;
				background-color: #F6F6F6;
				padding-bottom: 200rpx;
				padding-top: 100rpx;
				.card{
					margin-top: 40rpx;
					width: 717rpx;
					// min-height: 342rpx;
					background: url('/static/img/storebg.jpg') no-repeat center top/100% 100%;
					border-radius: 20rpx;
					padding: 45rpx 30rpx 25rpx;
					position: relative;
					color: #181411;
					.title{
						width: 100%;
						padding-left: 26rpx;
						font-size: 34rpx;
					}
					.address{
						margin-top: 20rpx;
						padding-left: 26rpx;
						display: flex;
						.addricon{
							margin-top: 5rpx;
							width: 30rpx;
						}
						.text{
							margin-left: 10rpx;
							font-size: 30rpx;
							max-width: 440rpx;
						}
					}
					.line{
						width: 100%;
						height: 1px;
					}
					.tel_content{
						display: flex;
						flex-direction: column;
						margin:30rpx 0;
						.tel_box{
							display: flex;
							align-items: center;
							text{
								margin-top: 4rpx;
								font-size: 26rpx;
								color: #6B6666;
							}
							image{
								width: 20rpx;
							}
						}
					}
					.photo-wall{
						margin-top: 20rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;
						.storephoto{
							width: 136rpx;
							height: 136rpx;
							border-radius: 10rpx;
						}
					}
					.navi{
						width: 65rpx;
						height: 65rpx;
						position: absolute;
						top: 104rpx;
						right: 95rpx;
					}
				}
			}
			.button1{
				width: 458rpx;
				height: 84rpx;
				background: linear-gradient(180deg, #F95959, #F73030);
				border-radius: 42rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				margin: 76rpx 0;
			}
		}

	}
</style>