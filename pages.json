{
	"easycom": {
		"autoscan": true,
		"custom": {
			"^tm-(.*)": "@/tmui/components/tm-$1/tm-$1.vue"
		}
	},
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "皖嫂家政",
				"navigationStyle": "custom",
				"onReachBottomDistance":300
			}
		},
		// #ifdef APP-PLUS || H5
		{
			"path": "pages/login/index",
			"style": {
				"navigationBarTitleText": "登录",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/forgot",
			"style": {
				"navigationBarTitleText": "忘记密码"
			}
		},
		{
			"path": "pages/login/register",
			"style": {
				"navigationBarTitleText": "用户注册"
			}
		},
		// #endif
		{
			"path": "pages/login/phone",
			"style": {
				"navigationBarTitleText": "手机号登陆"
			}
		},
		{
			"path": "pages/resume/index",
			"style": {
				"navigationBarTitleText": "皖嫂简历"
			}
		},
		{
			"path": "pages/more_one/index",
			"style": {
				"navigationBarTitleText": "阿姨相册"
			}
		},
		{
			"path": "pages/more_two/index",
			"style": {
				"navigationBarTitleText": "雇主评价"
			}
		},
		{
			"path": "pages/center/index",
			"style": {
				"navigationBarTitleText": "个人中心"
			}
		},
		{
			"path": "pages/intr/index",
			"style": {
				"navigationBarTitleText": "皖嫂家政"
			}
		},
		{
			"path": "pages/order/index",
			"style": {
				"navigationBarTitleText": "预约阿姨",
				"app-plus": {
					"animationType": "none"
				}
			}
		},
		{
			"path": "pages/editUserInfo/index",
			"style": {
				"navigationBarTitleText": "个人资料"
			}
		},
		{
			"path": "pages/store/index",
			"style": {
				"navigationBarTitleText": "皖嫂门店"
			}
		},
		{
			"path": "pages/flow/index",
			"style": {
				"navigationBarTitleText": "服务流程"
			}
		},
		{
			"path": "pages/protecte/index",
			"style": {
				"navigationBarTitleText": "服务保障"
			}
		},
		{
			"path": "pages/meet/index",
			"style": {
				"navigationBarTitleText": "皖嫂见面会"
			}
		},
		{
			"path": "pages/chankang/index",
			"style": {
				"navigationBarTitleText": "产康体验券"
			}
		},
		{
			"path": "pages/recommend/index",
			"style": {
				"navigationBarTitleText": "推荐有礼"
			}
		},
		{
			"path": "pages/nanny/index",
			"style": {
				"navigationBarTitleText": "线上月嫂"
			}
		},
		{
			"path": "pages/nannyDetail/index",
			"style": {
				"navigationBarTitleText": "月嫂详情"
			}
		},
		{
			"path": "pages/moreComment/index",
			"style": {
				"navigationBarTitleText": "客户评价"
			}
		},
		{
			"path": "pages/newsDetail/index",
			"style": {
				"navigationBarTitleText": "皖嫂资讯"
			}
		},
		{
			"path": "pages/train/index",
			"style": {
				"navigationBarTitleText": "上工/培训"
			}
		},
		{
			"path": "pages/screen/index",
			"style": {
				"navigationBarTitleText": "皖嫂预约"
			}
		},
		{
			"path": "pages/order/myorder",
			"style": {
				"navigationBarTitleText": "我的订单"
			}
		},
		{
			"path": "pages/welfare/index",
			"style": {
				"navigationBarTitleText": "新人福利"
			}
		},
		{
			// #ifdef APP-PLUS 
			"path" : "pages/nannyVideo/index2",
			// #endif 
			// #ifndef APP-PLUS 
			"path" : "pages/nannyVideo/index",
			// #endif 
		    "style" :                                                                                    
		    {
				"navigationBarTitleText": "视频月嫂",
		        "app-plus":{
		        	"titleNView":false,
		        	"bounce":"none"
		        }
				// #ifdef MP-WEIXIN
				,"navigationStyle": "custom"
				// #endif 
		    }
		},
		{
			// #ifdef APP-PLUS 
			"path" : "pages/classroomVideo/index2",
			// #endif 
			// #ifndef APP-PLUS 
			"path" : "pages/classroomVideo/index",
			// #endif 
		    "style" :                                                                                    
		    {
				"navigationBarTitleText": "孕妈课堂",
		        "app-plus":{
		        	"titleNView":false,
		        	"bounce":"none"
		        }
				// #ifdef MP-WEIXIN
				,"navigationStyle": "custom"
				// #endif 
		    }
		},
		{
			"path": "pages/web/index"
		},
		{
			"path" : "pages/video/video",
			"style" : 
			{
				"navigationBarTitleText" : "双人音视频对话"
			}
		}
    ],
	"tabBar": {
		"color": "#fff",
		"height": "0px" ,
		"selectedColor": "#fff",
		"borderStyle": "white",
		"backgroundColor": "#ffffff",
		"list": [
			{
				"pagePath": "pages/index/index",
				"text": "首页"
			}, 
			{
				"pagePath": "pages/center/index",
				"text": "个人中心"
			},
			{
				"pagePath": "pages/intr/index",
				"text": "皖嫂家政"
			},
			{
				"pagePath": "pages/order/index",
				"text": "预约阿姨"
			},
			{
				"pagePath": "pages/nanny/index",
				"text": "线上月嫂"
			}
		]
	},

	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "皖嫂家政",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#FFFFFF"
	},
	"condition": {
		"current": 0,
		"list": [
			{
				"name": "",
				"path": "", 
				"query": ""
			}
		]
	}
}