<template>
	<tm-app ref="app">
		<view class="main">
			<view class="header">
				<tm-image :width="718" :height="197" src="imageUrlPrefix/nanny-button1.png" _class="photo" @click="goLink('/pages/order/index')"></tm-image>
				<view class="bottom">
					<tm-image :width="350" :height="190" src="imageUrlPrefix/nanny-button2.png" _class="photo2"  @click="goLink('/pages/meet/index',{navtype:1})"></tm-image>
					<tm-image :width="350" :height="190" src="imageUrlPrefix/nanny-button3.png" _class="photo2"  @click="goLink('/pages/classroomVideo/index',{navtype:nowvideoshow?3:4,cate:3})"></tm-image>
				</view>
			</view>
			<view class="card-list" v-if="list[0]">	
				<view class="card"  v-for="item in list" :key="item.hid">
					<view class="top" @click="goLink('/pages/resume/index',{hid:item.hid})">
						<image :src="item.avatar||'imageUrlPrefix/avatar.png'" mode="aspectFill" class="avatar"></image>
						<view class="userinfo">
							<text class="name">{{item.name}} {{item.age}}</text>
							<text class="resume">{{item.experience}}年经验 {{item.from}}人</text>
							<text class="type">{{item.job[0]}}</text>
						</view>
						<image :src="item.grade_pic" mode="widthFix" class="core"></image>
					</view>
					<view class="center" v-if="item.intro" @click="goLink('/pages/resume/index',{hid:item.hid})">
						<kevy-ellipsis :content="item.intro" :rows="6" expand-text="展开" collapse-text="收起" :font-size="26"  />
					</view>
					<view class="photo-wall" v-if="item.photos&&item.photos[0]">
						<tm-image-group>
							<tm-image 
							:width="150" 
							:height="150" 
							:margin="[12,12]" 
							:round="6" 
							class="photo" 
							preview 
							:previewList="item.photos"
							previewName="file_path"
							:src="item2.imgpath"
							:key="item2.imgpath"
							model="aspectFill" 
							v-for="item2 in item.photos"></tm-image>
						</tm-image-group>
						<!-- <image :src="item2.imgpath" mode="aspectFill" class="photo" v-for="item2 in item.photos"></image> -->
					</view>
					<view class="comment" v-if="item.pj&&item.pj[0]" @click="goLink('/pages/resume/index',{hid:item.hid})">
						<image :src="item.pj[0].avatarUrl||'imageUrlPrefix/avatar.png'" mode="aspectFill" class="avatar"></image>
						<kevy-ellipsis :content="item.pj[0].nickName+'：'+item.pj[0].content" :rows="3" expand-text="展开" collapse-text="收起" :font-size="25"  />
					</view>
				</view>
			</view>
			<view class="loading">
				<tm-icon spin color="#d0d0d0" :font-size="24" name="tmicon-shuaxin"></tm-icon>
				<tm-text _class="ml-20" :font-size="24" color="#d0d0d0" label="加载中"></tm-text>
			</view>
			<!-- <view class="button1" @click="goLink('')">立即预约</view> -->
			<tabber></tabber>
		</view>
	</tm-app>
</template>
<script lang="ts" setup>
	import { ref, computed, watch } from "vue"
	import { onShow, onLoad,onReachBottom} from '@dcloudio/uni-app'
	import tmApp from "@/tmui/components/tm-app/tm-app.vue"
	import tmImage from '@/tmui/components/tm-image/tm-image2.vue'
	import tmText from '@/tmui/components/tm-text/tm-text.vue'
	import { share } from "@/tmui/tool/lib/share";
	import tabber from '@/components/tabber/tabber.vue'
	import kevyEllipsis from '@/components/kevy-ellipsis/kevy-ellipsis'
	import * as api from '@/api/index.js'
	import { goLink } from '@/until/index'
	import { useStore } from '@/until/mainpinia';
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const list = ref([])
	const page = ref(1)
	const getData = ()=>{
		api.request.ajax({
			url: '/aunt/lists',
			type: 'POST',
			whiteList: true,
			data:{page:page.value}
		}).then(res => {
			if(res.code===1){
				list.value = list.value.concat(res.data)
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	onLoad(() => {
		getData()
	})
	onReachBottom(()=>{
		page.value++
		getData()
	})
	const store = useStore()
	const nowvideoshow = computed(()=>store.nowvideoshow)
</script>
<style lang="less" scoped>
	.main {
		width: 750rpx;
		overflow-x: hidden;
		padding-bottom: 200rpx;
		background: #F4F3F3;
		display: flex;
		flex-direction: column;
		align-items: center;
		.loading{
			width: 750rpx;
			height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.header{
			width: 718rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-top: 30rpx;
			.photo{
				width: 718rpx;
				height: 197rpx;
			}
			.bottom{
				margin-top: 30rpx;
				width: 100%;
				display: flex;
				justify-content: space-between;
				.photo2{
					width: 350rpx;
					height: 190rpx;
				}
			}
		}
		.card-list{
			width: 750rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			.card{
				margin-top: 40rpx;
				width: 736rpx;
				background: #FFFFFF;
				border-radius: 20rpx;
				padding: 40rpx 20rpx;
				.top{
					width: 100%;
					display: flex;
					align-items: center;
					.avatar{
						width: 130rpx;
						height: 130rpx;
						border-radius: 50%;
						border: 4rpx solid #F9CDB5;
					}
					.userinfo{
						display: flex;
						flex-direction: column;
						margin-left: 30rpx;
						.name{
							font-size: 30rpx;
							color: #303133;
						}
						.resume{
							font-size: 24rpx;
							font-weight: 300;
							color: #6A6363;
						}
						.type{
							font-size: 24rpx;
							font-weight: 300;
							color: #6A6363;
						}
					}
					.core{
						width: 160rpx;
						height: 160rpx;
						margin-left: 80rpx;
					}
				}
				.center{
					width: 100%;
					margin-top: 30rpx;
					font-size: 25rpx;
					/* font-weight: 300; */
					color: #6A6363;
					line-height: 35rpx;
					text-align: justify;
				}
				.photo-wall{
					width: 100%;
					margin: -12rpx;
					margin-top: 25rpx;
					display: flex;
					flex-wrap: wrap;
					.photo{
						width: 150rpx;
						height: 150rpx;
						border-radius: 10rpx;
						margin: 12rpx;
					}
				}
				.comment{
					margin-top: 45rpx;
					width: 100%;
					display: flex;
					font-weight: 300;
					.avatar{
						width: 48rpx !important;
						height: 48rpx !important;
						border-radius: 50%;
						flex-shrink: 0;
						margin-right: 14rpx;
					}
					.text{
						flex:1;
						font-size: 25rpx;
						font-weight: 300;
						color: #6A6363;
						line-height: 35rpx;
					}
				}
			}
		}
		
		.button1 {
			width: 458rpx;
			height: 84rpx;
			background: linear-gradient(180deg, #F95959, #F73030);
			border-radius: 42rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin: 20rpx 0 40rpx;
			font-size: 36rpx;
			color: #FFFFFF;
			line-height: 84px;
			letter-spacing: 4rpx;
		}
	}
</style>