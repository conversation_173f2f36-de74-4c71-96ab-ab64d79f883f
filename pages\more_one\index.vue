<template>
	<tm-app ref="app">
		<view class="main">
			<view class="normal-pad">
				<view class="pad-content">
					<view class="tabcont" v-if="photoTab.length>1">
						<view class="tabitem righttab" :class="active===index?'active':''" v-for="(item ,index) in photoTab" :key="'tab'+index" @click="change(index)">
							{{item.title}}
						</view>
					</view>
					<view class="p-content" v-for="(item ,index) in photoTab" :key="'photo'+index">
						<view :class="active===index?'':'noshow'">
							<tm-image-group>
								<tm-image :width="144" :height="144" :margin="[6,6]" :round="6" preview
									:previewList="resumePhoto[item.index]" previewName="file_path" :src="item2.imgpath"
									:key="item2.imgpath" model="aspectFill" v-for="item2 in resumePhoto[item.index]"></tm-image>
							</tm-image-group>
							<view class="nomore" v-if="!resumePhoto[item.index][0]">暂无图片</view>
							<view class="nomore" v-if="photoTab[active]?.nomore">已经到底了</view>
							<view class="nomore" v-if="!photoTab[active]?.nomore" @click="getmore">点击加载更多+</view>
						</view>

					</view>
				</view>
			</view>
		</view>
	</tm-app>
</template>
<script lang="ts" setup>
	import { ref, computed, getCurrentInstance } from "vue"
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import tmApp from "@/tmui/components/tm-app/tm-app.vue"
	import tmImage from '@/tmui/components/tm-image/tm-image2.vue'
	import * as api from '@/api/index.js'
	import { goLink } from '@/until/index'
	import { useStore } from '@/until/mainpinia';
	import { share } from "@/tmui/tool/lib/share";
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()

	// 页面数据
	const store = useStore()
	const hid = ref('')
	onLoad((p) => {
		hid.value = p.hid
		getData(hid.value)
	})
	const photoTab = ref([])
	const resumePhoto = ref({})
	const active = ref(0)
	const getData = (hid : string) => {
		api.request.ajax({
			url: '/aunt/newauntPhotos',
			type: 'POST',
			whiteList: true,
			data: { 
				hid,
				page:3
			}
		}).then(res => {
			if (res.code === 1) {
				photoTab.value = res.data.photoTab
				resumePhoto.value = res.data.resumePhoto
				photoTab.value.forEach(item=>{
					if(resumePhoto.value[item.index].length<res.data.limit){
						item.nomore = true
					}
					item.page = 1
				})
			} else {
				uni.showToast({ title: res.msg, icon: 'none' })
			}
		})
	}
	const change = (i?:number)=>{
		if(i!==undefined){
			active.value = i
		}
	}
	const getmore = ()=>{
		if(photoTab.value[active.value].nomore){
			return false
		}
		photoTab.value[active.value].page+=1
		api.request.ajax({
			url: '/aunt/getPhotoList',
			type: 'POST',
			whiteList: true,
			data: { 
				hid:hid.value,
				page:photoTab.value[active.value].page,
				photo_type:photoTab.value[active.value].index
			}
		}).then(res => {
			if (res.code === 1) {
				if(res.data.photos[0]){
					resumePhoto.value[photoTab.value[active.value].index] = resumePhoto.value[photoTab.value[active.value].index].concat(res.data.photos)
				}else{
					photoTab.value[active.value].nomore = true
				}
			} else {
				uni.showToast({ title: res.msg, icon: 'none' })
			}
		})
	}
</script>
<style lang="less" scoped>
	.main {
		width: 750rpx;
		min-height: 100vh;
		padding-bottom: 100rpx;
		background: #fff;
		display: flex;
		flex-direction: column;
		align-items: center;

		.normal-pad {
			width: 690rpx;
			background: #fff;
			border-radius: 30rpx;
			margin: 30rpx 30rpx 0 30rpx;
			box-shadow: 0 0 10rpx #d3d3d3;
			border-radius: 30rpx;
			position: relative;

			.pad-content {
				padding: 30rpx;
				// display: flex;
				// flex-direction: column;
				// align-items: center;
				.tabcont{
				  display: flex;
				  align-items: center;
				  justify-content: center;
				  margin-bottom: 25rpx;
				}
				.tabitem{
				  padding: 20rpx 2%;
				  border: 1rpx solid #d71e00;
				  text-align:center;
				  border-left: none;
				  width: 160rpx;
				  &:nth-child(1){
					  border-left: 1rpx solid #d71e00;
						border-radius: 20rpx 0 0 20rpx;
				  }
				  &:nth-last-child(1){
					  border-radius:0 20rpx 20rpx 0;
				  }
				}
				.active{
				      background: #d71e00;
				      color: #fff;
				  }
				  .nomore{
					width: 100%;
				    color: #999;
				    text-align: center;
				    font-size: 30rpx;
				    margin: 20rpx 0;
					position: static;
				  }
				.noshow{
					height: 0;
					width: 0;
					overflow: hidden;
					display: none;
					opacity: 0;
				}
			}
		}

	}
</style>