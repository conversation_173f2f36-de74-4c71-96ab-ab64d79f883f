<template>
	<view class="card" v-if="props.content.type===1" @click="handleClick">
		<tm-image :width="354" :height="480" :src="props.content.pics[0]" model="aspectFill" _class="main-img"></tm-image>
		<view class="text">
			{{props.content.content}}
			<!-- <kevy-ellipsis :content="props.content.content" :rows="5" :font-size="26" fontColor="#000"  v-if="props.content.pics[0]"  /> -->
		</view>
		
		<view class="bottom">
			<tm-avatar :size="40" :img="props.content.avatarUrl||'imageUrlPrefix/avatar.png'"></tm-avatar>
			<tm-text parentClass="ml-10" :font-size="25" color="#9e9595" :label="props.content.nickName"></tm-text>
		</view>
	</view>
<!-- 	<view class="card" v-if="props.content.type===2" @click="handleClick">
		<image mode="aspectFill" :src="props.content.imgLink" class="main-img"></image>
	</view> -->
	<view class="card" :id="'videoarea'+props.content._id" v-if="props.content.type===3" @click="handleClick">
		<view class="main-video">
			<!-- #ifndef APP-PLUS -->
			<video
			class="main-video" 
			object-fit="cover"
			:poster="props.content.cover"
			:src="props.content.src"
			:autoplay="playstart"
			:loop="true"
			:muted="props.content.isplay"
			:controls="false"
			:http-cache="true"
			:page-gesture="false"
			:show-fullscreen-btn="false"
			:show-loading="false"
			:show-center-play-btn="false"
			:enable-progress-gesture="false"
			v-if="playstart"
			></video>
			<!-- #endif -->
			<!-- #ifdef APP-PLUS -->
			<video
			class="main-video" 
			object-fit="cover"
			:poster="props.content.cover"
			:src="props.content.src"
			:autoplay="true"
			:loop="true"
			:muted="props.content.isplay"
			:controls="false"
			:http-cache="true"
			:page-gesture="false"
			:show-fullscreen-btn="false"
			:show-loading="false"
			:show-center-play-btn="false"
			:enable-progress-gesture="false"
			v-if="playstart"
			>
			    <cover-view @click="handleClick" class="pull"></cover-view>  
			</video>
			<!-- #endif -->
			<tm-image :width="354" :height="600" :src="props.content.cover" model="aspectFill" _class="main-video" v-if="!playstart"></tm-image>
		</view>
		<view class="video_text" v-if="props.content.cate==='1'">
			<image :src="props.content.href" mode="widthFix" class="avatar"></image>
			<view class="right">
				{{props.content.username}} {{props.content.level_text}}
			</view>
		</view>
		<view class="cover" v-if="props.content.cate==='2'">
			<image src="/static/img/mmtk.png" mode="widthFix" class="mmtk_icon"></image>
			<view class="mmkt_text">孕妈课堂</view>
		</view>
		<view class="cover" v-if="props.content.cate==='3'">
			<image src="/static/img/mmtk.png" mode="widthFix" class="mmtk_icon"></image>
			<view class="mmkt_text">阿姨视频</view>
		</view>
		<!-- #ifdef APP-PLUS -->
		<cover-view class="cover" v-if="playstart&&props.content.cate==='2'">
			<cover-image src="/static/img/mmtk.png" mode="widthFix" class="mmtk_icon"></cover-image>
			<cover-view class="mmkt_text">孕妈课堂</cover-view>
		</cover-view>
		<cover-view class="cover" v-if="playstart&&props.content.cate==='3'">
			<cover-image src="/static/img/mmtk.png" mode="widthFix" class="mmtk_icon"></cover-image>
			<cover-view class="mmkt_text">阿姨视频</cover-view>
		</cover-view>
		<!-- #endif -->

		
	</view>
</template>

<script lang="ts" setup>
	import { ref, PropType, computed, getCurrentInstance, nextTick,onMounted } from 'vue'
	import kevyEllipsis from '@/components/kevy-ellipsis/kevy-ellipsis'
	import tmText from '@/tmui/components/tm-text/tm-text.vue'
	const emits = defineEmits(['click'])
	const props = defineProps({
		content: {
			type: Object,
			default: () => {
				return {}
			}
		}
	})
	const handleClick = () => {
		emits('click', JSON.parse(JSON.stringify(props.content)))
	}
	/** 监控视频是否需要播放 */
	const playstart = ref(false)
	if(props.content.type===3){
		let {screenWidth, screenHeight} = uni.getSystemInfoSync()
		let topBottomPadding = (screenHeight - 300)/2
		// #ifdef MP
		const app2 = getCurrentInstance()
		const videoObserve = uni.createIntersectionObserver(app2)
		videoObserve.relativeToViewport({bottom: -300, top: -300})
		.observe(`.card`, (res) => {
			let {intersectionRatio} = res
			if(intersectionRatio === 0) {
				//离开视界，因为视窗占比为0，停止播放
				
				// console.log('离开',props.content.id);
				// setTimeout(()=>{
					playstart.value = false
				// },500)
			}else{
				//进入视界，开始播放
				
				// console.log('进入',props.content.id);
				playstart.value = true
			}
		})
		// #endif
		// #ifdef APP-PLUS||H5
		const videoarea = ref(null)
		setTimeout(()=>{
			const app2 = getCurrentInstance()
			const SelectorQuery = uni.createSelectorQuery()
			SelectorQuery.select(`#videoarea${props.content._id}`).boundingClientRect(data=>{
				if(!data)return
				if(data.top<0||data.top>screenHeight-600){
					playstart.value = false
				}else{
					playstart.value = true
				}
			})
			const getHeight = ()=>{
				SelectorQuery.exec()
			}
			setInterval(()=>{
				getHeight()
			},500)
		},10)
		
		// #endif
	}

</script>

<style lang="less" scoped>
	.card {
		width: 354rpx;
		overflow: hidden;
		// min-height: 400rpx;
		background: #FFFFFF;
		border-radius: 12rpx;
		margin-bottom: 22rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		.main-img {
			width: 354rpx;
			height: 480rpx;
		}

		.text {
			width: 330rpx;
			margin-top: 22rpx;
			padding: 0 10rpx;
			text-overflow: -o-ellipsis-lastline;  
			overflow: hidden;  
			text-overflow: ellipsis;  
			display: -webkit-box;  
			-webkit-line-clamp: 5;  
			-webkit-box-orient: vertical;  
			font-size: 26rpx;
			line-height: 1.5;
			text-align: justify;
			color: #000;
		}

		.bottom {
			width: 330rpx;
			margin-top: 16rpx;
			margin-bottom: 12rpx;
			// padding-left: 24rpx;
			display: flex;
			flex-direction: row;
			align-items: center;
		}

		.main-video {
			width: 354rpx;
			height: 600rpx;
			border-radius: 12rpx 12rpx 0 0;
			z-index: 1;
			// overflow: hidden;
			-webkit-border-top-left-radius: 12rpx;
			-webkit-border-top-right-radius: 12rpx;
			-webkit-border-bottom-left-radius: 0;
			-webkit-border-bottom-right-radius: 0;
		}
		.pull{
			width: 100%;
			height: 100%;
		}
		.video_text{
			width: 100%;
			padding: 14rpx 10rpx 24rpx;
			display: flex;
			flex-direction: row;
			align-items: center;
			.avatar{
				width: 40rpx;
				height: 40rpx;
				border-radius: 50%;
				
			}
			.right{
				margin-left: 10rpx;
				color: #000;
				font-size: 25rpx;
				white-space:nowrap;
				overflow:hidden;
			}
		}
		.cover{
			width: 160rpx;
			height: 40rpx;
			border-radius: 20rpx;
			background-color: rgba(0,0,0,.6);
			position: absolute;
			z-index: 2;
			left: 10rpx;
			top: 20rpx;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			.mmtk_icon{
				width: 30rpx;
				height: 30rpx;
			}
			.mmkt_text{
				margin-left: 6rpx;
				font-size: 24rpx;
				color: #fff;
			}
		}
	}
</style>