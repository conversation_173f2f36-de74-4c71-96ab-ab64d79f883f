import { ref } from 'vue'
import * as api from '@/api/index.js'

export function useSmsCode() {
  const time = ref(0)
  const showImageCode = ref(false)
  let timer: number | null = null
  
  // 开始倒计时
  const startCountdown = () => {
    time.value = 60
    timer = setInterval(() => {
      if(time.value > 0) {
        time.value--
      } else {
        if(timer) {
          clearInterval(timer)
          timer = null
        }
      }
    }, 1000)
  }
  
  // 停止倒计时
  const stopCountdown = () => {
    time.value = 0
    if(timer) {
      clearInterval(timer)
      timer = null
    }
  }

  // 验证手机号
  const validatePhone = (phone: string) => {
    if(!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(phone)) {
      uni.showToast({
        title: '请填写正确的手机号',
        icon: 'none'
      })
      return false
    }
    return true
  }

  // 发送短信验证码
  const sendSmsCode = async (phone: string, imgcode: string) => {
    if(!validatePhone(phone)) return false
    
    try {
      const res = await api.request.ajax({
        url: '/config/sendmsg2',
        type: 'POST',
        whiteList: true,
        data: {
          phone,
          imgyzm:imgcode
        }
      })
      
      if(res.code === 1) {
        uni.showToast({ title: '发送成功', icon: 'none' })
        startCountdown()
        return true
      } else {
        uni.showToast({ title: res.msg, icon: 'none' })
        stopCountdown()
        return false
      }
    } catch(e) {
      uni.showToast({ title: '发送失败', icon: 'none' })
      stopCountdown()
      return false
    }
  }

  // 处理图形验证码成功
  const handleImageCodeSuccess = async (phone: string, code: string) => {
    return await sendSmsCode(phone, code)
  }

  // 点击发送验证码
  const handleSendCode = (phone: string) => {
    if(!validatePhone(phone)) return
    showImageCode.value = true
  }

  return {
    time,
    showImageCode,
    handleSendCode,
    handleImageCodeSuccess,
    stopCountdown
  }
} 