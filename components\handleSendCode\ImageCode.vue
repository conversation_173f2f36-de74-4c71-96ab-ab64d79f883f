<template>
  <view class="image-code" v-if="show">
    <view class="mask" @click="close"></view>
    <view class="content">
      <view class="title">请输入图形验证码</view>
      <view class="code-area">
        <image :src="imageUrl" mode="widthFix" class="code-img" @click="refreshCode"></image>
        <input 
          type="text" 
          v-model="code"
          placeholder="请输入验证码" 
          placeholder-style="color:#909399"
          class="code-input"
        />
      </view>
      <view class="btn-group">
        <view class="btn cancel" @click="close">取消</view>
        <view class="btn confirm" @click="confirm">确定</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref,watch } from 'vue'
import * as api from '@/api/index.js'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'success'])

const imageUrl = ref('')
const code = ref('')

// 获取图形验证码
const getImageCode = () => {
  // 直接使用接口URL + 时间戳(防止缓存)
  imageUrl.value = api.request.options.baseUrl + '/config/imgcode?t=' + Date.now()
}

// 刷新验证码
const refreshCode = () => {
  getImageCode()
}

// 关闭弹框
const close = () => {
  emit('update:show', false)
  code.value = ''
}

// 确认
const confirm = () => {
  if(!code.value) {
    uni.showToast({
      title: '请输入验证码',
      icon: 'none'
    })
    return
  }
  emit('success', code.value)
  close()
}

// 监听显示
watch(() => props.show, (val) => {
  if(val) {
    getImageCode()
  }
})
</script>

<style lang="less" scoped>
.image-code {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  
  .mask {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
  }
  
  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 280px;
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    
    .title {
      font-size: 16px;
      text-align: center;
      margin-bottom: 20px;
    }
    
    .code-area {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      
      .code-img {
        width: 100px;
        height: 40px;
        margin-right: 10px;
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
      }
      
      .code-input {
        flex: 1;
        height: 40px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 0 10px;
      }
    }
    
    .btn-group {
      display: flex;
      justify-content: space-between;
      
      .btn {
        width: 45%;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 20px;
        font-size: 14px;
        
        &.cancel {
          background: #f5f5f5;
          color: #666;
        }
        
        &.confirm {
          background: #2979ff;
          color: #fff;
        }
      }
    }
  }
}
</style> 