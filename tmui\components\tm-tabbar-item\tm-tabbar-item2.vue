<template>
	<view class="flex flex-col flex-col-top-center" style="height: 75px">
		<tm-sheet
			:height="60"
			:width="_width"
			:round="24"
			unit="px"
			_class="flex-center flex "
			parenClass="relative"
			class="relative"
			:_style="_styletop"
			:followTheme="_btnTop && props.followTheme"
			:transprent="_transprent"
			:color="props.color"
			:margin="[0, 0]"
			:padding="_padding"
			:shadow="props.shadow"
			:outlined="props.outlined"
			:border="props.border"
			:borderStyle="props.borderStyle"
			:borderDirection="props.borderDirection"
			:linear="props.linear"
			:linearDeep="props.linearDeep"
			@click="itemClick"
		>
			<tm-badge
				:fontSize="20"
				:color="c_font_style.dotColor"
				:eventPenetrationEnabled="true"
				:dot="props.dot"
				:count="props.count"
				:icon="props.dotIcon"
				:maxCount="props.maxCount"
			>
				<view :class="[_active ? 'anifun' : '']" class="flex flex-col flex-col-center-center" :style="{ width: 65 + 'px', height: '30px' }">
					<slot>
						<tm-icon
							:customicon="props.customicon"
							v-if="!_load&&!props.maintabicon"
							_style="line-height: 0px;"
							:color="_color"
							:font-size="c_font_style.iconSize"
							:name="_active ? c_font_style.icon : c_font_style.unicon || c_font_style.icon"
						></tm-icon>
					</slot>
					<tm-icon
						v-if="_load"
						spin
						_style="line-height: 0px;"
						:color="_color"
						:font-size="c_font_style.iconSize"
						name="tmicon-shuaxin"
					></tm-icon>
					<image v-if="props.maintabicon" class="tabicon" src="data:image/png;base64,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" mode="widthFix"></image>
				</view>
			</tm-badge>
			<tm-text
				v-if="c_font_style.text !== ''"
				:color="_color"
				_class="pb-0"
				:font-size="c_font_style.textSize"
				:label="c_font_style.text"
			></tm-text>
		</tm-sheet>
	</view>
</template>

<script lang="ts" setup>
/**
 * 底部导航栏项目
 * @description 只能放置在tm-tabbar中。
 */
import tmBadge from '../tm-badge/tm-badge.vue'
import tmText from '../tm-text/tm-text.vue'
import tmIcon from '../tm-icon/tm-icon.vue'
import tmSheet from '../tm-sheet/tm-sheet.vue'
import { custom_props } from '../../tool/lib/minxs'
import { useTmpiniaStore } from '../../tool/lib/tmpinia'
import {
	getCurrentInstance,
	computed,
	watchEffect,
	ref,
	provide,
	inject,
	onUpdated,
	onMounted,
	onUnmounted,
	nextTick,
	watch,
	PropType,
	ComponentInternalInstance
} from 'vue'
const store = useTmpiniaStore()
/**
 * click 项目被点击时触发。
 * beforeClick点击切换之前执行，如果返回false或者Promise<false>时，将阻止链接的切换。如果没有提供url链接地地址将只作为切换使用。
 */
const emits = defineEmits(['click', 'beforeClick'])
const proxy = getCurrentInstance()?.proxy ?? null
const props = defineProps({
	...custom_props,
	followTheme: {
		type: [Boolean, String],
		default: true
	},
	transprent: {
		type: Boolean,
		default: true
	},
	//背景主题色
	color: {
		type: String,
		default: 'white'
	},
	//默认的文字主题
	fontColor: {
		type: String,
		default: 'grey-darken-1'
	},
	linear: {
		type: String,
		default: ''
	},
	//激活后的主题色。
	activeColor: {
		type: String,
		default: 'primary'
	},
	//当前是否是活动项。
	active: {
		type: Boolean,
		default: false
	},
	//是否开启向上凸起的按钮。
	btnTop: {
		type: Boolean,
		default: false
	},
	text: {
		type: String,
		default: ''
	},
	icon: {
		type: String,
		default: ''
	},
	//未选中时的图标，如果不填写默认使用相同的图标。
	unicon: {
		type: String,
		default: ''
	},
	textSize: {
		type: Number,
		default: 20
	},
	iconSize: {
		type: Number,
		default: 38
	},
	dot: {
		type: [Boolean],
		default: false
	},
	dotColor: {
		type: [String],
		default: 'red'
	},
	dotIcon: {
		type: [String],
		default: ''
	},
	//如果count为数字时，显示数字角标，如果为string是显示文本角标。
	count: {
		type: [Number, String],
		default: 0
	},
	maxCount: {
		type: [Number],
		default: 99
	},
	url: {
		type: [String],
		default: ''
	},
	//链接打开方式同官方。
	openType: {
		type: String as PropType<'navigate' | 'redirect' | 'switchTab' | 'reLaunch' | 'navigateBack'>,
		default: 'navigate'
	},
	//打开链接之前执行的勾子函数，返回fase阻止打开。也可以返回new Promise.
	beforeClick: {
		type: [Function, Boolean],
		default: () => false
	},
	load: {
		type: [Boolean, String],
		default: false
	},
	maintabicon: {
		type: [Boolean, String],
		default: false
	},
	//自定项目数据，在执行beforeClick时，会返回给函数内部
	data: {
		type: [Object, String, Number],
		default: () => undefined
	},
	/**是否禁用选项. */
	disabled: {
		type: Boolean,
		default: false
	},
	/**
	 * 为了提高响应速度，只有开启了自定图标显示功能才会去解析用户自定义图标规则名称
	 */
	customicon: {
		type: Boolean,
		default: false
	}
})
const _btnTop = computed(() => props.btnTop)
const _transprent = computed(() => {
	if (_btnTop.value === true) return false
	return true
})
const _styletop = computed(() => {
	if (_btnTop.value !== true) return 'top:15px'
	return 'top:0px;border:12rpx solid #fff'
})

const _padding = computed(() => {
	return [0, 0]
})
const _disabled = computed(() => props.disabled)
const _load = ref(props.load)
const _active = ref(false)
const c_font_style = computed(() => {
	return {
		dotColor: props.dotColor,
		text: props.text,
		icon: props.icon,
		textSize: props.textSize,
		iconSize: props.iconSize,
		unicon: props.unicon
	}
})
const uid = uni.$tm.u.getUid(1)
const tmTabbarWidth = inject(
	'tmTabbarWidth',
	computed(() => 50)
)
const _width = computed(() => {
	if (_btnTop.value === true) return 60
	return tmTabbarWidth.value
})
const nowUrl = inject(
	'tmTabbarUrl',
	computed(() => '')
)
const tmTabbarItemList = inject(
	'tmTabbarItemList',
	computed<Array<string | number>>(() => [])
)
const nowUid = inject(
	'tmTabbarUid',
	computed<string | number>(() => '')
)
const tmTabbarItemSafe = inject('tmTabbarItemSafe', false)
const tmTabbarItemActive = inject(
	'tmTabbarItemActive',
	computed(() => -1)
)
const tmTabbarItemAutoSelect = inject(
	'tmTabbarItemAutoSelect',
	computed(() => false)
)
const _color = computed(() => {
	if (_active.value === true && !_btnTop.value) {
		if (store.tmStore.color && props.followTheme) {
			return store.tmStore.color
		}
		return props.activeColor
	}
	return props.fontColor
})

//父级方法。
let parent: any = proxy?.$parent
while (parent) {
	if (parent?.tmTabbarId == 'tmTabbarId' || !parent) {
		break
	} else {
		parent = parent?.$parent ?? undefined
	}
}
if (parent) {
	parent.pushKey(uid)
}
onUnmounted(() => {
	if (parent) {
		parent.delKey(uid)
	}
})

if (tmTabbarItemAutoSelect.value) {
	_active.value = props.active || false
} else {
	if (tmTabbarItemList.value[tmTabbarItemActive.value] == uid) {
		_active.value = true
	} else {
		_active.value = false
	}
}
function setActive() {
	if (nowUid.value == uid) {
		_active.value = true
	} else {
		_active.value = false
	}
}

watch([nowUid, () => props.active], () => {
	if (tmTabbarItemAutoSelect.value) {
		setActive()
	}
})
watch(tmTabbarItemActive, () => {
	if (!tmTabbarItemAutoSelect.value) {
		// 非自动选中，通过父组件的active来选中当前。
		if (tmTabbarItemList.value[tmTabbarItemActive.value] == uid) {
			nextTick(() => {
				_active.value = true
			})
		} else {
			nextTick(() => {
				_active.value = false
			})
		}
	}
})
watch([() => props.load], () => {
	_load.value = props.load
})
async function itemClick() {
	if (_load.value || _disabled.value) return
	if (typeof props.beforeClick === 'function') {
		_load.value = true
		let p = await props.beforeClick(props.data)
		if (typeof p === 'function') {
			p = await p(props.data)
		}
		_load.value = false
		if (!p) return
	}

	emits('click')
	nextTick(() => {
		if (tmTabbarItemAutoSelect.value) {
			if (parent) {
				parent.setNowurl(props.url, uid)
			}
			setActive()
		}
		if (props.url == '') return
		uni.$tm.u.routerTo(props.url, props.openType)
	})
}
</script>

<style scoped>
/* #ifndef APP-NVUE */
.anifun {
	animation: scale 0.2s ease;
}
.tabicon{
	width: 151rpx;
	height: 149rpx;
	flex-shrink: 0;
}
@keyframes scale {
	0% {
		transform: scale(0.9);
	}

	50% {
		transform: scale(1.1);
	}

	100% {
		transform: scale(1);
	}
}

/* #endif */
</style>
